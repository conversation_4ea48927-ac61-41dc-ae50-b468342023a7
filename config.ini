# ----------------------------------------------------------------------------
# This is the Amass configuration file.
#
# You can find more information about how to configure amass here:
# https://github.com/owasp-amass/amass/blob/master/examples/config.ini
# ----------------------------------------------------------------------------

[default]
# Add any global settings here if needed.

# ----------------------------------------------------------------------------
# SECTION FOR API KEYS
#
# Amass can be configured to use various online data sources.
# API keys are now loaded from environment variables for security.
# Set these in your .env file or environment:
# - ABUSEIPDB_API_KEY
# - ALIENVAULT_API_KEY
# - SECURITYTRAILS_API_KEY
# - SHODAN_API_KEY
# - VIRUSTOTAL_API_KEY
# ----------------------------------------------------------------------------

[data_sources]

# AbuseIPDB - https://www.abuseipdb.com/account
# apikey = ${ABUSEIPDB_API_KEY}

# AlienVault - https://otx.alienvault.com/
# apikey = ${ALIENVAULT_API_KEY}

# SecurityTrails - https://securitytrails.com/
# apikey = ${SECURITYTRAILS_API_KEY}

# Shodan - https://account.shodan.io/
# apikey = ${SHODAN_API_KEY}

# VirusTotal - https://www.virustotal.com/
# apikey = ${VIRUSTOTAL_API_KEY}

# BinaryEdge - https://app.binaryedge.io/account
#apikey = YOUR_API_KEY_HERE

# BufferOver - https://tls.bufferover.run/
#apikey = YOUR_API_KEY_HERE

# Censys - https://censys.io/register
#id = YOUR_ID_HERE
#secret = YOUR_SECRET_HERE

# Chaos - https://chaos.projectdiscovery.io/
#apikey = YOUR_API_KEY_HERE

# DNSDB - https://www.dnsdb.info/
#apikey = YOUR_API_KEY_HERE

# GitHub - https://github.com/settings/tokens
#apikey = YOUR_API_KEY_HERE

# Hunter - https://hunter.io/
#apikey = YOUR_API_KEY_HERE

# IntelX - https://intelx.io/
#apikey = YOUR_API_KEY_HERE

# PassiveTotal - https://community.riskiq.com/
#username = YOUR_USERNAME_HERE
#apikey = YOUR_API_KEY_HERE

# Spyse - https://spyse.com/
#apikey = YOUR_API_KEY_HERE

# ThreatBook - https://threatbook.io/
#apikey = YOUR_API_KEY_HERE

# WhoisXMLAPI - https://www.whoisxmlapi.com/
#apikey = YOUR_API_KEY_HERE

# Zero-X - https://zer0-x.com/
#apikey = YOUR_API_KEY_HERE
