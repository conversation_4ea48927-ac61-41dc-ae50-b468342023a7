/**
 * Main Application File
 * Enhanced ASM System with modular architecture
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

// Import configuration and utilities
const { config } = require('./config');
const logger = require('./utils/logger');

// Import middleware
const { 
    errorHandler, 
    notFoundHandler 
} = require('./middleware/errorHandler');
const {
    cors,
    helmet,
    createRateLimiter,
    securityHeaders,
    requestLogger
} = require('./middleware/security');

// Import routes
const scanRoutes = require('./api/routes/scanRoutes');

// Import services
const scanService = require('./services/scanService');
const aiService = require('./services/aiService');

class ASMApplication {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: config.security.corsOrigin,
                methods: ["GET", "POST"]
            }
        });
        
        this.port = config.server.port;
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
        this.setupErrorHandling();
    }

    setupMiddleware() {
        // Security middleware
        this.app.use(helmet);
        this.app.use(cors);
        this.app.use(securityHeaders);
        
        // Rate limiting
        const generalRateLimit = createRateLimiter();
        this.app.use('/api/', generalRateLimit);
        
        // Request logging
        this.app.use(requestLogger);
        
        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
        
        // Static files
        this.app.use(express.static(path.join(__dirname, '../public')));
        
        logger.info('Middleware setup completed');
    }

    setupRoutes() {
        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({
                success: true,
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: process.env.npm_package_version || '1.0.0',
                environment: config.server.nodeEnv
            });
        });

        // API status endpoint
        this.app.get('/api/status', (req, res) => {
            const activeSessions = scanService.getAllSessions();
            res.json({
                success: true,
                status: 'online',
                activeSessions: activeSessions.length,
                timestamp: new Date().toISOString(),
                services: {
                    ai: !!aiService,
                    scanner: !!scanService
                }
            });
        });

        // API routes
        this.app.use('/api/scan', scanRoutes);

        // Serve dashboard
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../public', 'dashboard.html'));
        });

        logger.info('Routes setup completed');
    }

    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            logger.info('Client connected', { socketId: socket.id });

            // Join scan session room
            socket.on('join-session', (sessionId) => {
                socket.join(sessionId);
                const session = scanService.getSession(sessionId);
                
                if (session) {
                    socket.emit('session-update', session);
                    logger.debug('Client joined session', { socketId: socket.id, sessionId });
                } else {
                    socket.emit('error', { message: 'Session not found' });
                }
            });

            // Handle chat messages
            socket.on('send-message', async (data) => {
                const { sessionId, message } = data;
                const session = scanService.getSession(sessionId);
                
                if (!session) {
                    socket.emit('error', { message: 'Session not found' });
                    return;
                }

                try {
                    // Add user message to chat history
                    const userMessage = {
                        type: 'user',
                        message,
                        timestamp: new Date().toISOString()
                    };
                    session.chatHistory.push(userMessage);

                    // Get AI response
                    const aiResponse = await aiService.chatInteraction(
                        message, 
                        session.chatHistory,
                        { 
                            sessionId, 
                            target: session.target, 
                            findings: session.findings 
                        }
                    );

                    // Add AI response to chat history
                    const aiMessage = {
                        type: 'ai',
                        message: aiResponse.analysis,
                        command: aiResponse.command,
                        timestamp: new Date().toISOString()
                    };
                    session.chatHistory.push(aiMessage);

                    // Update session
                    scanService.updateSession(sessionId, session);

                    // Emit updated session to all clients in the room
                    this.io.to(sessionId).emit('session-update', session);

                    // If AI suggested a command, execute it
                    if (aiResponse.command) {
                        this.executeAICommand(sessionId, aiResponse.command, socket);
                    }

                } catch (error) {
                    logger.error('Chat message processing failed', { 
                        sessionId, 
                        error: error.message 
                    });
                    socket.emit('error', { message: 'Failed to process message' });
                }
            });

            // Handle disconnection
            socket.on('disconnect', () => {
                logger.info('Client disconnected', { socketId: socket.id });
            });
        });

        logger.info('Socket handlers setup completed');
    }

    async executeAICommand(sessionId, command, socket) {
        try {
            const session = scanService.getSession(sessionId);
            if (!session) return;

            // Emit command start
            this.io.to(sessionId).emit('command-start', { command });

            // Set up real-time output streaming
            session.onOutput = (data) => {
                this.io.to(sessionId).emit('command-output', { data });
            };

            // Execute command
            const result = await scanService.executeCommand(sessionId, command, 'ai');

            // Remove output callback
            delete session.onOutput;

            // Get AI analysis of the result
            const aiAnalysis = await aiService.processCommandOutput(
                command,
                result.output,
                { sessionId, target: session.target, findings: session.findings }
            );

            // Add AI analysis to chat
            const analysisMessage = {
                type: 'ai',
                message: aiAnalysis.analysis,
                command: aiAnalysis.command,
                timestamp: new Date().toISOString()
            };
            session.chatHistory.push(analysisMessage);

            // Update session
            scanService.updateSession(sessionId, session);

            // Emit command completion and session update
            this.io.to(sessionId).emit('command-complete', { command, result });
            this.io.to(sessionId).emit('session-update', session);

        } catch (error) {
            logger.error('AI command execution failed', { 
                sessionId, 
                command, 
                error: error.message 
            });
            
            this.io.to(sessionId).emit('command-error', { 
                command, 
                error: error.message 
            });
        }
    }

    setupErrorHandling() {
        // 404 handler
        this.app.use(notFoundHandler);
        
        // Global error handler
        this.app.use(errorHandler);
        
        logger.info('Error handling setup completed');
    }

    start() {
        this.server.listen(this.port, () => {
            logger.info('ASM System started', {
                port: this.port,
                environment: config.server.nodeEnv,
                url: `http://localhost:${this.port}`
            });
            
            console.log(`
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  🤖 Enhanced ASM System - Interactive AI Dashboard          ║
║                                                              ║
║  🌐 Web Interface: http://localhost:${this.port}                     ║
║  📊 API Status:    http://localhost:${this.port}/api/status          ║
║  🏥 Health Check:  http://localhost:${this.port}/health              ║
║                                                              ║
║  Environment: ${config.server.nodeEnv.toUpperCase().padEnd(10)}                                   ║
║  Log Level:   ${config.server.logLevel.toUpperCase().padEnd(10)}                                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
            `);
        });

        // Graceful shutdown
        process.on('SIGTERM', () => this.shutdown());
        process.on('SIGINT', () => this.shutdown());
    }

    shutdown() {
        logger.info('Shutting down ASM System...');
        
        this.server.close(() => {
            logger.info('Server closed');
            process.exit(0);
        });
    }
}

module.exports = ASMApplication;
