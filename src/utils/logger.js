/**
 * Enhanced Logging System
 * Provides structured logging with different levels and output formats
 */

const fs = require('fs');
const path = require('path');
const { config } = require('../config');

class Logger {
    constructor() {
        this.logLevel = config.server.logLevel;
        this.logDir = path.join(process.cwd(), 'logs');
        this.ensureLogDirectory();
        
        // Log levels with numeric values for comparison
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3,
            trace: 4
        };
        
        this.colors = {
            error: '\x1b[31m', // Red
            warn: '\x1b[33m',  // Yellow
            info: '\x1b[36m',  // Cyan
            debug: '\x1b[35m', // Magenta
            trace: '\x1b[37m', // White
            reset: '\x1b[0m'
        };
    }

    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }

    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const pid = process.pid;
        
        const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            pid,
            message,
            ...meta
        };

        return logEntry;
    }

    writeToFile(level, logEntry) {
        const filename = level === 'error' ? 'error.log' : 'combined.log';
        const filepath = path.join(this.logDir, filename);
        const logLine = JSON.stringify(logEntry) + '\n';
        
        fs.appendFileSync(filepath, logLine);
    }

    writeToConsole(level, message, meta = {}) {
        if (!this.shouldLog(level)) return;

        const timestamp = new Date().toISOString();
        const color = this.colors[level] || this.colors.reset;
        const reset = this.colors.reset;
        
        let output = `${color}[${timestamp}] ${level.toUpperCase()}:${reset} ${message}`;
        
        if (Object.keys(meta).length > 0) {
            output += ` ${JSON.stringify(meta)}`;
        }
        
        console.log(output);
    }

    log(level, message, meta = {}) {
        if (!this.shouldLog(level)) return;

        const logEntry = this.formatMessage(level, message, meta);
        
        // Write to console
        this.writeToConsole(level, message, meta);
        
        // Write to file
        this.writeToFile(level, logEntry);
    }

    error(message, meta = {}) {
        this.log('error', message, meta);
    }

    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }

    info(message, meta = {}) {
        this.log('info', message, meta);
    }

    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }

    trace(message, meta = {}) {
        this.log('trace', message, meta);
    }

    // Specialized logging methods for ASM operations
    scanStart(sessionId, target, scanType) {
        this.info('Scan started', {
            sessionId,
            target,
            scanType,
            event: 'scan_start'
        });
    }

    scanComplete(sessionId, target, duration, findings) {
        this.info('Scan completed', {
            sessionId,
            target,
            duration,
            findings: {
                subdomains: findings.subdomains?.length || 0,
                ports: findings.openPorts?.length || 0,
                vulnerabilities: findings.vulnerabilities?.length || 0
            },
            event: 'scan_complete'
        });
    }

    scanError(sessionId, target, error) {
        this.error('Scan error', {
            sessionId,
            target,
            error: error.message,
            stack: error.stack,
            event: 'scan_error'
        });
    }

    commandExecuted(command, exitCode, duration) {
        this.debug('Command executed', {
            command,
            exitCode,
            duration,
            event: 'command_executed'
        });
    }

    aiInteraction(sessionId, userMessage, aiResponse, processingTime) {
        this.debug('AI interaction', {
            sessionId,
            userMessageLength: userMessage.length,
            aiResponseLength: aiResponse.length,
            processingTime,
            event: 'ai_interaction'
        });
    }

    securityEvent(event, details) {
        this.warn('Security event', {
            event,
            ...details,
            event_type: 'security'
        });
    }

    // Performance monitoring
    performance(operation, duration, metadata = {}) {
        this.info('Performance metric', {
            operation,
            duration,
            ...metadata,
            event: 'performance'
        });
    }

    // Audit logging
    audit(action, user, resource, result) {
        this.info('Audit log', {
            action,
            user,
            resource,
            result,
            event: 'audit'
        });
    }
}

// Create singleton instance
const logger = new Logger();

module.exports = logger;
