/**
 * AI Service Module
 * Handles all AI interactions and intelligent decision making
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const { config } = require('../config');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

class AIService {
    constructor() {
        this.initializeAI();
        this.systemPrompt = this.getEnhancedSystemPrompt();
    }

    initializeAI() {
        const apiKey = config.ai.geminiApiKey;
        if (!apiKey) {
            throw new AppError('GEMINI_API_KEY not configured', 500, 'AI_CONFIG_ERROR');
        }
        
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ 
            model: config.ai.model,
            generationConfig: {
                maxOutputTokens: config.ai.maxTokens,
                temperature: config.ai.temperature
            }
        });
        
        logger.info('AI service initialized', { model: config.ai.model });
    }

    getEnhancedSystemPrompt() {
        return `You are an Advanced AI Attack Surface Management (ASM) System with the following capabilities:

**CORE INTELLIGENCE:**
- Autonomous decision making for reconnaissance strategies
- Real-time risk assessment and prioritization
- Intelligent correlation of findings across multiple tools
- Adaptive scanning based on discovered assets
- Continuous learning from scan results

**ENHANCED CAPABILITIES:**
1. **Smart Reconnaissance:** Automatically determine the best tool sequence based on target type
2. **Risk-Based Prioritization:** Focus on high-risk findings first
3. **Intelligent Correlation:** Connect findings across different scan types
4. **Adaptive Scanning:** Modify approach based on discovered assets
5. **Real-time Analysis:** Provide insights as data comes in

**OPERATIONAL MODES:**
- PASSIVE: Subdomain enumeration, DNS analysis, OSINT
- ACTIVE: Port scanning, service detection, vulnerability assessment
- CONTINUOUS: Ongoing monitoring and change detection
- EMERGENCY: Rapid assessment for incident response

**DECISION FRAMEWORK:**
For each target, you should:
1. Analyze target type (domain, IP, organization)
2. Determine optimal reconnaissance strategy
3. Execute tools in logical sequence
4. Correlate findings in real-time
5. Assess risk levels continuously
6. Provide actionable intelligence

**RESPONSE FORMAT:**
Always structure responses with:
- ANALYSIS: What you discovered
- RISK_LEVEL: LOW/MEDIUM/HIGH/CRITICAL
- NEXT_ACTION: Recommended next step
- COMMAND: Single bash command in code block (if applicable)
- REASONING: Why this approach is optimal

**SECURITY CONSIDERATIONS:**
- Always validate targets before scanning
- Respect rate limits and ethical boundaries
- Prioritize stealth in reconnaissance
- Focus on actionable vulnerabilities
- Provide clear remediation guidance

You are running on Parrot OS Security Edition with access to professional penetration testing tools.`;
    }

    async analyzeTarget(target, scanType = 'auto') {
        try {
            const prompt = `Analyze the target "${target}" and determine the optimal scanning strategy.
            
Scan type requested: ${scanType}

Please provide:
1. Target analysis (domain/IP/organization)
2. Recommended scanning approach
3. Tool sequence and rationale
4. Expected findings and risks
5. First command to execute

Format your response according to the system prompt structure.`;

            const startTime = Date.now();
            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const aiText = response.text();
            const processingTime = Date.now() - startTime;

            logger.aiInteraction('target_analysis', prompt, aiText, processingTime);

            return this.parseAIResponse(aiText);
        } catch (error) {
            logger.error('AI target analysis failed', { target, error: error.message });
            throw new AppError('AI analysis failed', 500, 'AI_ANALYSIS_ERROR');
        }
    }

    async processCommandOutput(command, output, context = {}) {
        try {
            const prompt = `Analyze the output from the command: ${command}

Output:
${output}

Context: ${JSON.stringify(context)}

Please provide:
1. Analysis of the results
2. Risk assessment of findings
3. Correlation with previous findings
4. Next recommended action
5. Command to execute next (if applicable)

Format your response according to the system prompt structure.`;

            const startTime = Date.now();
            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const aiText = response.text();
            const processingTime = Date.now() - startTime;

            logger.aiInteraction('command_analysis', prompt, aiText, processingTime);

            return this.parseAIResponse(aiText);
        } catch (error) {
            logger.error('AI command analysis failed', { command, error: error.message });
            throw new AppError('AI command analysis failed', 500, 'AI_ANALYSIS_ERROR');
        }
    }

    async chatInteraction(message, chatHistory = [], sessionContext = {}) {
        try {
            const contextPrompt = `Session Context: ${JSON.stringify(sessionContext)}

Chat History:
${chatHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

User Message: ${message}

Please respond as an ASM expert, providing guidance, analysis, or executing commands as appropriate.`;

            const startTime = Date.now();
            const result = await this.model.generateContent(contextPrompt);
            const response = await result.response;
            const aiText = response.text();
            const processingTime = Date.now() - startTime;

            logger.aiInteraction('chat', message, aiText, processingTime);

            return this.parseAIResponse(aiText);
        } catch (error) {
            logger.error('AI chat interaction failed', { message, error: error.message });
            throw new AppError('AI chat interaction failed', 500, 'AI_CHAT_ERROR');
        }
    }

    parseAIResponse(text) {
        const analysis = this.extractSection(text, 'ANALYSIS');
        const riskLevel = this.extractSection(text, 'RISK_LEVEL');
        const nextAction = this.extractSection(text, 'NEXT_ACTION');
        const reasoning = this.extractSection(text, 'REASONING');
        const command = this.parseCommand(text);

        return {
            analysis: analysis || 'No analysis provided',
            riskLevel: this.normalizeRiskLevel(riskLevel),
            nextAction: nextAction || 'Continue assessment',
            reasoning: reasoning || 'No reasoning provided',
            command: command,
            fullResponse: text
        };
    }

    extractSection(text, sectionName) {
        const regex = new RegExp(`${sectionName}:\\s*([^\\n]*(?:\\n(?!\\w+:)[^\\n]*)*)`, 'i');
        const match = text.match(regex);
        return match ? match[1].trim() : null;
    }

    parseCommand(text) {
        const match = text.match(/```bash\n([\s\S]*?)\n```/);
        return match ? match[1].trim() : null;
    }

    normalizeRiskLevel(riskLevel) {
        if (!riskLevel) return 'UNKNOWN';
        
        const normalized = riskLevel.toUpperCase();
        const validLevels = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
        
        return validLevels.includes(normalized) ? normalized : 'UNKNOWN';
    }

    async generateScanStrategy(target, findings = {}) {
        try {
            const prompt = `Generate an adaptive scanning strategy for target: ${target}

Current findings:
- Subdomains: ${findings.subdomains?.length || 0}
- Open ports: ${findings.openPorts?.length || 0}
- Vulnerabilities: ${findings.vulnerabilities?.length || 0}

Based on current findings, what should be the next phase of scanning?
Provide a prioritized list of commands and rationale.`;

            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const aiText = response.text();

            return this.parseAIResponse(aiText);
        } catch (error) {
            logger.error('AI strategy generation failed', { target, error: error.message });
            throw new AppError('AI strategy generation failed', 500, 'AI_STRATEGY_ERROR');
        }
    }

    async assessRisk(findings) {
        try {
            const prompt = `Assess the overall risk level based on these findings:

Findings:
${JSON.stringify(findings, null, 2)}

Provide:
1. Overall risk assessment
2. Critical vulnerabilities to prioritize
3. Recommended immediate actions
4. Long-term security recommendations`;

            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const aiText = response.text();

            return this.parseAIResponse(aiText);
        } catch (error) {
            logger.error('AI risk assessment failed', { error: error.message });
            throw new AppError('AI risk assessment failed', 500, 'AI_RISK_ERROR');
        }
    }
}

// Create singleton instance
const aiService = new AIService();

module.exports = aiService;
