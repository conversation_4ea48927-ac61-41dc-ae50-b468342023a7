/**
 * Black-G Engine - Core processing engine for AI-driven penetration testing
 * Handles natural language processing, tool selection, and execution coordination
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const logger = require('../utils/logger');

class BlackGEngine {
    constructor(apiKey) {
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
        this.chatHistory = [];
        this.currentContext = null;
    }

    async initialize() {
        const systemPrompt = this.getSystemPrompt();
        this.chatHistory = [
            {
                role: 'user',
                parts: [{ text: systemPrompt }]
            },
            {
                role: 'model',
                parts: [{ text: 'Black-G Engine initialized. Ready for security testing requests.' }]
            }
        ];
    }

    getSystemPrompt() {
        return `You are the Black-G Engine, an advanced AI system for penetration testing and Attack Surface Management.

CORE MISSION:
Transform natural language security testing requests into structured, executable penetration testing workflows.

CAPABILITIES:
- Parse natural language requests for security testing objectives
- Select appropriate tools and techniques for each objective
- Generate step-by-step execution plans
- Assess risks and provide recommendations
- Correlate findings across multiple tools and techniques

ASM CATEGORIES TO ANALYZE:
1. Domain/IP Vulnerabilities - Network and application vulnerabilities
2. SSL/TLS Certificate Analysis - Certificate security and configuration
3. Configuration Issues - System and service misconfigurations
4. Open Ports & Services - Network service enumeration and analysis
5. IP/Domain Reputation - Threat intelligence and reputation analysis
6. Cloud Security Assessment - Cloud-specific security issues
7. Authentication Discovery - Authentication mechanisms and weaknesses

AVAILABLE TOOLS:
- nmap: Network discovery and security auditing
- subfinder: Subdomain discovery
- amass: Attack surface mapping
- nuclei: Vulnerability scanner
- sslscan: SSL/TLS scanner
- masscan: High-speed port scanner
- gobuster: Directory/file brute forcer

RESPONSE FORMAT:
Structure all responses with these sections:
🎯 OBJECTIVE: Clear understanding of the request
📊 STRATEGY: Approach and methodology
🔍 EXECUTION_PLAN: Detailed step-by-step plan
⚠️ RISK_ASSESSMENT: Potential risks and considerations
📋 EXPECTED_OUTCOMES: Anticipated results

For each tool command, use this format:
COMMAND: [tool] [arguments]
DESCRIPTION: What this command accomplishes
CATEGORY: Which ASM category this addresses

ETHICAL GUIDELINES:
- Only suggest scans for authorized targets
- Recommend appropriate scan intensities
- Consider stealth and detection avoidance
- Prioritize minimal impact on target systems
- Follow responsible disclosure principles`;
    }

    async processRequest(userInput, context = {}) {
        try {
            this.currentContext = context;
            
            // Add user input to chat history
            this.chatHistory.push({
                role: 'user',
                parts: [{ text: userInput }]
            });

            // Get AI response
            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(userInput);
            const response = await result.response;
            const aiResponse = response.text();

            // Add AI response to chat history
            this.chatHistory.push({
                role: 'model',
                parts: [{ text: aiResponse }]
            });

            // Parse and structure the response
            const structuredResponse = this.parseResponse(aiResponse);
            
            logger.info('Black-G Engine processed request', {
                input: userInput.substring(0, 100),
                responseLength: aiResponse.length,
                commandsFound: structuredResponse.commands.length
            });

            return structuredResponse;

        } catch (error) {
            logger.error('Black-G Engine processing failed', { error: error.message });
            throw new Error(`Engine processing failed: ${error.message}`);
        }
    }

    parseResponse(response) {
        const structured = {
            objective: this.extractSection(response, 'OBJECTIVE'),
            strategy: this.extractSection(response, 'STRATEGY'),
            executionPlan: this.extractSection(response, 'EXECUTION_PLAN'),
            riskAssessment: this.extractSection(response, 'RISK_ASSESSMENT'),
            expectedOutcomes: this.extractSection(response, 'EXPECTED_OUTCOMES'),
            commands: this.extractCommands(response),
            rawResponse: response
        };

        return structured;
    }

    extractSection(text, sectionName) {
        const patterns = {
            'OBJECTIVE': /🎯\s*OBJECTIVE:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            'STRATEGY': /📊\s*STRATEGY:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            'EXECUTION_PLAN': /🔍\s*EXECUTION[_\s]*PLAN:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            'RISK_ASSESSMENT': /⚠️\s*RISK[_\s]*ASSESSMENT:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            'EXPECTED_OUTCOMES': /📋\s*EXPECTED[_\s]*OUTCOMES:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i
        };

        const pattern = patterns[sectionName];
        if (!pattern) return null;

        const match = text.match(pattern);
        return match ? match[1].trim() : null;
    }

    extractCommands(response) {
        const commands = [];
        const lines = response.split('\n');
        
        let currentCommand = null;
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            if (trimmedLine.startsWith('COMMAND:')) {
                if (currentCommand) {
                    commands.push(currentCommand);
                }
                currentCommand = {
                    command: trimmedLine.substring(8).trim(),
                    description: '',
                    category: '',
                    tool: this.extractTool(trimmedLine.substring(8).trim())
                };
            } else if (trimmedLine.startsWith('DESCRIPTION:') && currentCommand) {
                currentCommand.description = trimmedLine.substring(12).trim();
            } else if (trimmedLine.startsWith('CATEGORY:') && currentCommand) {
                currentCommand.category = trimmedLine.substring(9).trim();
            }
        }
        
        if (currentCommand) {
            commands.push(currentCommand);
        }
        
        return commands;
    }

    extractTool(command) {
        const parts = command.split(' ');
        return parts[0] || 'unknown';
    }

    async analyzeResults(results, originalRequest) {
        try {
            const analysisPrompt = `Analyze the following penetration testing results:

Original Request: ${originalRequest}

Results:
${this.formatResultsForAnalysis(results)}

Please provide:
1. Summary of key findings
2. Risk assessment (LOW/MEDIUM/HIGH/CRITICAL)
3. Security implications
4. Recommended next steps
5. Remediation priorities

Focus on actionable insights and professional security assessment.`;

            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(analysisPrompt);
            const response = await result.response;
            const analysis = response.text();

            logger.info('Black-G Engine analyzed results', {
                resultsCount: results.length,
                analysisLength: analysis.length
            });

            return {
                analysis: analysis,
                timestamp: new Date().toISOString(),
                resultsSummary: this.summarizeResults(results)
            };

        } catch (error) {
            logger.error('Results analysis failed', { error: error.message });
            throw new Error(`Results analysis failed: ${error.message}`);
        }
    }

    formatResultsForAnalysis(results) {
        return results.map((result, index) => {
            return `
Command ${index + 1}: ${result.command}
Tool: ${result.tool}
Category: ${result.category}
Status: ${result.error ? 'FAILED' : 'SUCCESS'}
${result.error ? `Error: ${result.error}` : `Output: ${result.result?.output?.substring(0, 500) || 'No output'}...`}
---`;
        }).join('\n');
    }

    summarizeResults(results) {
        return {
            total: results.length,
            successful: results.filter(r => !r.error).length,
            failed: results.filter(r => r.error).length,
            tools: [...new Set(results.map(r => r.tool))],
            categories: [...new Set(results.map(r => r.category).filter(Boolean))]
        };
    }

    getContext() {
        return {
            chatHistory: this.chatHistory,
            currentContext: this.currentContext,
            sessionInfo: {
                messagesCount: this.chatHistory.length,
                lastActivity: new Date().toISOString()
            }
        };
    }

    clearContext() {
        this.chatHistory = this.chatHistory.slice(0, 2); // Keep system prompt and initial response
        this.currentContext = null;
        logger.info('Black-G Engine context cleared');
    }
}

module.exports = BlackGEngine;
