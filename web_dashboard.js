require('dotenv').config();

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { GoogleGenerativeAI } = require('@google/generative-ai');

class InteractiveASMDashboard {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        
        this.port = process.env.PORT || 3000;
        this.activeSessions = new Map();
        this.scanResults = new Map();
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
        this.initializeAI();
    }

    setupMiddleware() {
        this.app.use(cors());
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, 'public')));
    }

    initializeAI() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            console.error('[ERROR] GEMINI_API_KEY environment variable not set.');
            return;
        }
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
    }

    setupRoutes() {
        // API Routes
        this.app.get('/api/status', (req, res) => {
            res.json({
                status: 'online',
                activeSessions: this.activeSessions.size,
                timestamp: new Date().toISOString()
            });
        });

        this.app.get('/api/sessions', (req, res) => {
            const sessions = Array.from(this.activeSessions.entries()).map(([id, session]) => ({
                id,
                target: session.target,
                status: session.status,
                startTime: session.startTime,
                findings: session.findings || {}
            }));
            res.json(sessions);
        });

        this.app.post('/api/scan/start', async (req, res) => {
            const { target, scanType } = req.body;
            
            if (!target) {
                return res.status(400).json({ error: 'Target is required' });
            }

            const sessionId = this.generateSessionId();
            const session = {
                id: sessionId,
                target: target,
                scanType: scanType || 'auto',
                status: 'initializing',
                startTime: new Date().toISOString(),
                findings: {
                    subdomains: [],
                    openPorts: [],
                    vulnerabilities: [],
                    riskLevel: 'UNKNOWN'
                },
                chatHistory: []
            };

            this.activeSessions.set(sessionId, session);
            
            // Start AI-guided scan
            this.startAIGuidedScan(sessionId);
            
            res.json({ sessionId, message: 'Scan initiated' });
        });

        this.app.get('/api/scan/:sessionId', (req, res) => {
            const session = this.activeSessions.get(req.params.sessionId);
            if (!session) {
                return res.status(404).json({ error: 'Session not found' });
            }
            res.json(session);
        });

        // Serve the dashboard
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
        });
    }

    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`[+] Client connected: ${socket.id}`);

            socket.on('join-session', (sessionId) => {
                socket.join(sessionId);
                const session = this.activeSessions.get(sessionId);
                if (session) {
                    socket.emit('session-update', session);
                }
            });

            socket.on('send-message', async (data) => {
                const { sessionId, message } = data;
                const session = this.activeSessions.get(sessionId);
                
                if (session) {
                    // Add user message to chat history
                    session.chatHistory.push({
                        type: 'user',
                        message: message,
                        timestamp: new Date().toISOString()
                    });

                    // Get AI response
                    const aiResponse = await this.getAIResponse(session, message);
                    
                    session.chatHistory.push({
                        type: 'ai',
                        message: aiResponse.text,
                        command: aiResponse.command,
                        analysis: aiResponse.analysis,
                        timestamp: new Date().toISOString()
                    });

                    // Broadcast update to all clients in the session
                    this.io.to(sessionId).emit('session-update', session);

                    // Execute command if present
                    if (aiResponse.command) {
                        this.executeCommand(sessionId, aiResponse.command);
                    }
                }
            });

            socket.on('execute-command', (data) => {
                const { sessionId, command } = data;
                this.executeCommand(sessionId, command);
            });

            socket.on('disconnect', () => {
                console.log(`[-] Client disconnected: ${socket.id}`);
            });
        });
    }

    generateSessionId() {
        return 'asm_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async startAIGuidedScan(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) return;

        session.status = 'running';
        
        const initialPrompt = `Start an intelligent ASM assessment for target: ${session.target}. 
        Scan type: ${session.scanType}. 
        Determine the best reconnaissance strategy and provide the first command.`;

        const aiResponse = await this.getAIResponse(session, initialPrompt);
        
        session.chatHistory.push({
            type: 'ai',
            message: aiResponse.text,
            command: aiResponse.command,
            analysis: aiResponse.analysis,
            timestamp: new Date().toISOString()
        });

        // Broadcast initial AI response
        this.io.to(sessionId).emit('session-update', session);

        // Auto-execute first command if available
        if (aiResponse.command && session.scanType === 'auto') {
            setTimeout(() => {
                this.executeCommand(sessionId, aiResponse.command);
            }, 1000);
        }
    }

    async getAIResponse(session, userMessage) {
        try {
            const systemPrompt = this.getSystemPrompt(session);
            const chatHistory = [
                { role: 'user', parts: [{ text: systemPrompt }] },
                ...session.chatHistory.map(msg => ({
                    role: msg.type === 'user' ? 'user' : 'model',
                    parts: [{ text: msg.message }]
                }))
            ];

            const chat = this.model.startChat({ history: chatHistory });
            const result = await chat.sendMessage(userMessage);
            const response = await result.response;
            const aiText = response.text();

            // Parse AI response
            const command = this.parseCommand(aiText);
            const analysis = this.extractAnalysis(aiText);

            return {
                text: aiText,
                command: command,
                analysis: analysis
            };
        } catch (error) {
            console.error('[AI ERROR]', error);
            return {
                text: 'AI service temporarily unavailable. Please try again.',
                command: null,
                analysis: null
            };
        }
    }

    getSystemPrompt(session) {
        return `You are an Advanced AI ASM System conducting assessment for: ${session.target}

Current findings:
- Subdomains: ${session.findings.subdomains.length}
- Open Ports: ${session.findings.openPorts.length}
- Vulnerabilities: ${session.findings.vulnerabilities.length}
- Risk Level: ${session.findings.riskLevel}

Provide intelligent analysis and actionable commands. Always wrap commands in \`\`\`bash code blocks.
Focus on discovering new assets and assessing security posture.`;
    }

    parseCommand(text) {
        const match = text.match(/```bash\n([\s\S]*?)\n```/);
        return match ? match[1].trim() : null;
    }

    extractAnalysis(text) {
        // Extract key insights from AI response
        const lines = text.split('\n');
        const analysis = lines.filter(line => 
            line.includes('ANALYSIS:') || 
            line.includes('RISK:') || 
            line.includes('FINDING:')
        );
        return analysis.length > 0 ? analysis.join('\n') : null;
    }

    executeCommand(sessionId, command) {
        const session = this.activeSessions.get(sessionId);
        if (!session) return;

        console.log(`[+] Executing command for session ${sessionId}: ${command}`);

        // Emit command execution start
        this.io.to(sessionId).emit('command-start', { command });

        let stdout = '';
        let stderr = '';

        const parts = command.split(' ');
        const tool = parts[0];
        const args = parts.slice(1);

        const child = spawn(tool, args);

        child.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            
            // Real-time output streaming
            this.io.to(sessionId).emit('command-output', { 
                type: 'stdout', 
                data: output 
            });

            // Parse findings in real-time
            this.parseRealTimeFindings(session, tool, output);
        });

        child.stderr.on('data', (data) => {
            const output = data.toString();
            stderr += output;
            
            this.io.to(sessionId).emit('command-output', { 
                type: 'stderr', 
                data: output 
            });
        });

        child.on('close', (code) => {
            console.log(`[+] Command completed with code: ${code}`);
            
            // Store results
            this.storeCommandResults(sessionId, command, { stdout, stderr, code });
            
            // Emit completion
            this.io.to(sessionId).emit('command-complete', { 
                command, 
                code, 
                success: code === 0 
            });

            // Update session status
            this.io.to(sessionId).emit('session-update', session);
        });

        child.on('error', (error) => {
            console.error(`[ERROR] Command execution failed: ${error.message}`);
            this.io.to(sessionId).emit('command-error', { 
                command, 
                error: error.message 
            });
        });
    }

    parseRealTimeFindings(session, tool, output) {
        switch (tool) {
            case 'subfinder':
                const subdomains = output.match(/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g);
                if (subdomains) {
                    subdomains.forEach(sub => {
                        if (!session.findings.subdomains.includes(sub)) {
                            session.findings.subdomains.push(sub);
                        }
                    });
                }
                break;
                
            case 'nmap':
                const ports = output.match(/(\d+)\/tcp\s+open/g);
                if (ports) {
                    ports.forEach(port => {
                        const portNum = port.match(/(\d+)/)[1];
                        if (!session.findings.openPorts.includes(portNum)) {
                            session.findings.openPorts.push(portNum);
                        }
                    });
                }
                break;
                
            case 'nuclei':
                if (output.includes('[CRITICAL]') || output.includes('[HIGH]')) {
                    session.findings.riskLevel = 'HIGH';
                    session.findings.vulnerabilities.push({
                        severity: 'HIGH',
                        description: output.trim(),
                        timestamp: new Date().toISOString()
                    });
                }
                break;
        }

        // Update risk assessment
        this.updateRiskAssessment(session);
    }

    updateRiskAssessment(session) {
        const findings = session.findings;
        let riskScore = 0;

        // Calculate risk based on findings
        riskScore += findings.subdomains.length * 0.1;
        riskScore += findings.openPorts.length * 0.5;
        riskScore += findings.vulnerabilities.length * 2;

        if (riskScore > 10) {
            findings.riskLevel = 'HIGH';
        } else if (riskScore > 5) {
            findings.riskLevel = 'MEDIUM';
        } else if (riskScore > 0) {
            findings.riskLevel = 'LOW';
        }
    }

    storeCommandResults(sessionId, command, results) {
        const timestamp = new Date().toISOString();
        const filename = `${timestamp}_${sessionId}_${command.split(' ')[0]}.log`;
        const filepath = path.join(__dirname, 'scan_reports', filename);
        
        const logContent = `Session: ${sessionId}
Command: ${command}
Exit Code: ${results.code}
Timestamp: ${timestamp}

--- STDOUT ---
${results.stdout}

--- STDERR ---
${results.stderr}
`;
        
        fs.writeFileSync(filepath, logContent);
    }

    start() {
        // Create public directory if it doesn't exist
        const publicDir = path.join(__dirname, 'public');
        if (!fs.existsSync(publicDir)) {
            fs.mkdirSync(publicDir);
        }

        // Create the dashboard HTML file
        this.createDashboardHTML();

        this.server.listen(this.port, () => {
            console.log(`
🌐 ===============================================
   INTERACTIVE ASM DASHBOARD STARTED
   Access: http://localhost:${this.port}
===============================================

🎯 Features:
   • Real-time scan monitoring
   • Interactive AI chat interface
   • Live command execution
   • Risk assessment dashboard
   • Multi-session management

🚀 Ready to accept connections...
`);
        });
    }

    createDashboardHTML() {
        const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive ASM Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: #0a0a0a; color: #fff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center; }
        .header h1 { font-size: 2em; margin-bottom: 10px; }
        .container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; padding: 20px; min-height: calc(100vh - 120px); }
        .panel { background: #1a1a1a; border-radius: 10px; padding: 20px; overflow: hidden; }
        .panel h2 { color: #667eea; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .metric { background: #2a2a2a; padding: 15px; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .metric-label { font-size: 0.9em; color: #ccc; margin-top: 5px; }
        .chat-container { display: flex; flex-direction: column; height: 400px; min-height: 400px; }
        .chat-messages { flex: 1; overflow-y: auto; padding: 10px; background: #111; border-radius: 5px; margin-bottom: 15px; min-height: 250px; }
        .message { margin-bottom: 15px; padding: 10px; border-radius: 8px; word-wrap: break-word; }
        .message.user { background: #2a4a7a; margin-left: 20px; }
        .message.ai { background: #2a7a4a; margin-right: 20px; }
        .message.system { background: #7a4a2a; font-style: italic; }
        .input-group { display: flex; gap: 10px; flex-shrink: 0; }
        .input-group input { flex: 1; padding: 12px; border: none; border-radius: 5px; background: #333; color: #fff; font-size: 14px; }
        .input-group input:focus { outline: none; background: #444; }
        .input-group button { padding: 12px 20px; border: none; border-radius: 5px; background: #667eea; color: #fff; cursor: pointer; font-weight: bold; }
        .input-group button:hover { background: #5a6fd8; }
        .terminal { background: #000; color: #0f0; font-family: 'Courier New', monospace; padding: 15px; border-radius: 5px; height: 300px; overflow-y: auto; }
        .risk-high { color: #ff4444; }
        .risk-medium { color: #ffaa44; }
        .risk-low { color: #44ff44; }
        .findings-list { max-height: 200px; overflow-y: auto; }
        .finding-item { background: #2a2a2a; margin: 5px 0; padding: 8px; border-radius: 4px; font-size: 0.9em; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }
        .status-running { background: #ffaa44; }
        .status-completed { background: #44ff44; }
        .status-error { background: #ff4444; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Interactive ASM Dashboard</h1>
        <p>AI-Powered Attack Surface Management System</p>
    </div>

    <div class="container">
        <div class="panel">
            <h2>📊 Real-time Metrics</h2>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="subdomains-count">0</div>
                    <div class="metric-label">Subdomains</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="ports-count">0</div>
                    <div class="metric-label">Open Ports</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="vulns-count">0</div>
                    <div class="metric-label">Vulnerabilities</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="risk-level">UNKNOWN</div>
                    <div class="metric-label">Risk Level</div>
                </div>
            </div>

            <h3>🎯 Current Target</h3>
            <div id="current-target" style="background: #2a2a2a; padding: 10px; border-radius: 5px; margin: 10px 0;">
                No active session
            </div>

            <h3>🔍 Recent Findings</h3>
            <div id="findings-list" class="findings-list">
                <div class="finding-item">Start a new scan to see findings...</div>
            </div>
        </div>

        <div class="panel">
            <h2>💬 AI Assistant</h2>
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <div class="message system">
                        🤖 AI Assistant ready. Start a new scan or ask questions about ASM techniques.
                    </div>
                </div>
                <div class="input-group">
                    <input type="text" id="message-input" placeholder="Enter target domain or ask AI assistant..." autocomplete="off" />
                    <button onclick="sendMessage()" type="button">Send</button>
                    <button onclick="startNewScan()" type="button">New Scan</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="panel">
            <h2>💻 Live Terminal Output</h2>
            <div id="terminal" class="terminal">
                Welcome to Interactive ASM System\\n
                Ready to execute commands...\\n
            </div>
        </div>

        <div class="panel">
            <h2>📈 Session Status</h2>
            <div id="session-status">
                <div style="background: #2a2a2a; padding: 15px; border-radius: 5px;">
                    <span class="status-indicator status-completed"></span>
                    System Ready - No active scans
                </div>
            </div>

            <h3 style="margin-top: 20px;">🔧 Quick Actions</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                <button onclick="startPassiveScan()" style="padding: 10px; background: #2a7a4a; border: none; border-radius: 5px; color: white; cursor: pointer;">Passive Scan</button>
                <button onclick="startActiveScan()" style="padding: 10px; background: #7a4a2a; border: none; border-radius: 5px; color: white; cursor: pointer;">Active Scan</button>
                <button onclick="generateReport()" style="padding: 10px; background: #4a2a7a; border: none; border-radius: 5px; color: white; cursor: pointer;">Generate Report</button>
                <button onclick="clearSession()" style="padding: 10px; background: #7a2a2a; border: none; border-radius: 5px; color: white; cursor: pointer;">Clear Session</button>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let currentSessionId = null;

        // Socket event handlers
        socket.on('session-update', (session) => {
            updateDashboard(session);
        });

        socket.on('command-output', (data) => {
            appendToTerminal(data.data);
        });

        socket.on('command-complete', (data) => {
            appendToTerminal(\`\\n✅ Command completed: \${data.command}\\n\`);
        });

        // Dashboard functions
        function updateDashboard(session) {
            currentSessionId = session.id;

            // Update metrics
            document.getElementById('subdomains-count').textContent = session.findings.subdomains.length;
            document.getElementById('ports-count').textContent = session.findings.openPorts.length;
            document.getElementById('vulns-count').textContent = session.findings.vulnerabilities.length;

            const riskElement = document.getElementById('risk-level');
            riskElement.textContent = session.findings.riskLevel;
            riskElement.className = \`metric-value risk-\${session.findings.riskLevel.toLowerCase()}\`;

            // Update target
            document.getElementById('current-target').textContent = session.target;

            // Update chat
            updateChat(session.chatHistory);

            // Update findings
            updateFindings(session.findings);
        }

        function updateChat(chatHistory) {
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = '';

            chatHistory.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${msg.type}\`;
                messageDiv.innerHTML = \`
                    <strong>\${msg.type === 'user' ? '👤 You' : '🤖 AI'}:</strong>
                    <div>\${msg.message}</div>
                    \${msg.command ? \`<div style="background: #333; padding: 5px; margin-top: 5px; border-radius: 3px; font-family: monospace;">💻 \${msg.command}</div>\` : ''}
                \`;
                chatMessages.appendChild(messageDiv);
            });

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function updateFindings(findings) {
            const findingsList = document.getElementById('findings-list');
            findingsList.innerHTML = '';

            findings.subdomains.slice(-5).forEach(sub => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                item.textContent = \`🌐 Subdomain: \${sub}\`;
                findingsList.appendChild(item);
            });

            findings.openPorts.slice(-5).forEach(port => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                item.textContent = \`🔓 Open Port: \${port}\`;
                findingsList.appendChild(item);
            });
        }

        function appendToTerminal(text) {
            const terminal = document.getElementById('terminal');
            terminal.textContent += text;
            terminal.scrollTop = terminal.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();

            if (!message) return;

            if (currentSessionId) {
                socket.emit('send-message', {
                    sessionId: currentSessionId,
                    message: message
                });
            } else {
                // Start new session
                startNewScan(message);
            }

            input.value = '';
        }

        async function startNewScan(target) {
            if (!target) {
                target = prompt('Enter target domain or IP:');
            }

            if (!target) return;

            try {
                const response = await fetch('/api/scan/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: target, scanType: 'auto' })
                });

                const result = await response.json();
                currentSessionId = result.sessionId;

                socket.emit('join-session', currentSessionId);

                appendToTerminal(\`🚀 Started new scan session: \${currentSessionId}\\n\`);
                appendToTerminal(\`🎯 Target: \${target}\\n\`);

            } catch (error) {
                console.error('Error starting scan:', error);
                appendToTerminal(\`❌ Error starting scan: \${error.message}\\n\`);
            }
        }

        function startPassiveScan() {
            const target = prompt('Enter target for passive scan:');
            if (target) {
                startScanWithType(target, 'passive');
            }
        }

        function startActiveScan() {
            const target = prompt('Enter target for active scan:');
            if (target) {
                startScanWithType(target, 'active');
            }
        }

        async function startScanWithType(target, scanType) {
            try {
                const response = await fetch('/api/scan/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: target, scanType: scanType })
                });

                const result = await response.json();
                currentSessionId = result.sessionId;
                socket.emit('join-session', currentSessionId);

            } catch (error) {
                console.error('Error starting scan:', error);
            }
        }

        function generateReport() {
            if (currentSessionId) {
                window.open(\`/api/scan/\${currentSessionId}/report\`, '_blank');
            } else {
                alert('No active session to generate report for');
            }
        }

        function clearSession() {
            currentSessionId = null;
            document.getElementById('chat-messages').innerHTML = '<div class="message system">🤖 Session cleared. Ready for new scan.</div>';
            document.getElementById('terminal').textContent = 'Session cleared...\\n';

            // Reset metrics
            document.getElementById('subdomains-count').textContent = '0';
            document.getElementById('ports-count').textContent = '0';
            document.getElementById('vulns-count').textContent = '0';
            document.getElementById('risk-level').textContent = 'UNKNOWN';
            document.getElementById('current-target').textContent = 'No active session';
        }

        // Enter key support
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialize dashboard
        appendToTerminal('🤖 Interactive ASM Dashboard loaded\\n');
        appendToTerminal('💡 Start by entering a target domain or IP\\n');

        // Ensure input is visible and functional
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.focus();
                console.log('Message input found and focused');
            } else {
                console.error('Message input not found!');
            }
        });
    </script>
</body>
</html>`;

        const htmlPath = path.join(__dirname, 'public', 'dashboard.html');
        fs.writeFileSync(htmlPath, htmlContent);
        console.log('[+] Dashboard HTML created successfully');
    }
}

// Start the Interactive ASM Dashboard
const dashboard = new InteractiveASMDashboard();
dashboard.start();
