#!/usr/bin/env node

/**
 * Black-G CLI - Interactive AI-Driven Penetration Testing System
 * Specialized CLI for Attack Surface Management on Parrot OS
 * 
 * Features:
 * - Natural language processing for security testing requests
 * - Intelligent tool selection and execution
 * - Comprehensive ASM analysis across 7 core categories
 * - Professional report generation
 * - PuTTY/SSH compatible interface
 */

const readline = require('readline');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
require('dotenv').config();

// Import existing services and utilities
const { config } = require('./src/config');
const logger = require('./src/utils/logger');

// ANSI Colors for terminal output (PuTTY compatible)
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

class BlackGCLI {
    constructor() {
        this.genAI = null;
        this.model = null;
        this.chatHistory = [];
        this.currentSession = null;
        this.activeScan = false;
        this.scanResults = {};
        
        // Initialize components
        this.toolSelector = new ToolSelector();
        this.asmAnalyzer = new ASMAnalyzer();
        this.reporter = new BlackGReporter();
        
        this.initializeAI();
        this.setupReadline();
        this.showBanner();
    }

    initializeAI() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            this.printError('GEMINI_API_KEY environment variable not set.');
            this.printInfo('Please set your Gemini API key in the .env file');
            process.exit(1);
        }
        
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
        
        // Initialize chat with Black-G system prompt
        this.initializeChat();
    }

    setupReadline() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: `${colors.cyan}Black-G>${colors.reset} `
        });

        this.rl.on('line', (input) => this.handleUserInput(input.trim()));
        this.rl.on('close', () => this.shutdown());
    }

    showBanner() {
        console.clear();
        console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🤖 BLACK-G CLI - AI PENETRATION TESTING SYSTEM                   ║
║                                                                      ║
║    Specialized Attack Surface Management for Parrot OS              ║
║    Natural Language → Intelligent Tool Selection → Professional Reports ║
║                                                                      ║
║    🎯 ASM Categories: Domain Vulns • SSL/TLS • Config • Ports       ║
║                      Reputation • Cloud Security • Auth Discovery   ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝${colors.reset}

${colors.yellow}🚀 Welcome to Black-G CLI!${colors.reset}
${colors.white}Type your security testing requests in natural language.${colors.reset}

${colors.green}Examples:${colors.reset}
  • "Perform passive reconnaissance on example.com"
  • "Run comprehensive ASM analysis on 192.168.1.0/24"  
  • "Check SSL vulnerabilities on my web servers"
  • "Scan for open ports and services on target.com"

${colors.blue}Commands:${colors.reset} help | status | history | clear | exit

`);
        this.rl.prompt();
    }

    async initializeChat() {
        const systemPrompt = this.getBlackGSystemPrompt();
        this.chatHistory = [
            {
                role: 'user',
                parts: [{ text: systemPrompt }]
            },
            {
                role: 'model', 
                parts: [{ text: 'Black-G AI Penetration Testing Assistant initialized. Ready to assist with Attack Surface Management and security testing. Please describe your security testing objective.' }]
            }
        ];
    }

    getBlackGSystemPrompt() {
        return `You are Black-G, an advanced AI penetration testing assistant specialized in Attack Surface Management for Parrot OS Security Edition.

CORE IDENTITY:
- Expert penetration tester and security analyst
- Specialized in Attack Surface Management (ASM)
- Integrated with Parrot OS security tools
- Focused on professional, ethical security testing

CORE CAPABILITIES:
- Natural language interpretation of security testing requests
- Intelligent tool selection and execution planning
- Real-time analysis and risk assessment
- Professional report generation
- Comprehensive ASM analysis across 7 categories

ASM ANALYSIS CATEGORIES:
1. Domain/IP Vulnerabilities - Comprehensive vulnerability assessment
2. SSL/TLS Certificate Analysis - Certificate validation and security analysis  
3. Configuration Issues - Misconfigurations and security gaps
4. Open Ports & Services - Port scanning and service enumeration
5. IP/Domain Reputation - Threat intelligence and reputation checks
6. Cloud Security Assessment - Cloud-specific vulnerabilities
7. Authentication Discovery - Login page detection and analysis

AVAILABLE TOOLS (Parrot OS):
- nmap: Port scanning, service detection, OS fingerprinting
- subfinder: Subdomain enumeration
- amass: Attack surface mapping and asset discovery
- nuclei: Vulnerability scanning with templates
- sslscan: SSL/TLS configuration analysis
- masscan: High-speed port scanning
- gobuster: Directory/file brute forcing
- Custom scripts: Reputation checks, cloud analysis

OPERATIONAL MODES:
- PASSIVE: Subdomain enumeration, DNS analysis, OSINT (no direct target interaction)
- ACTIVE: Port scanning, service detection, vulnerability assessment
- COMPREHENSIVE: Full ASM analysis across all 7 categories
- TARGETED: Specific vulnerability or service analysis

RESPONSE FORMAT:
Always structure your responses with these sections:

🎯 OBJECTIVE: Clear understanding of the user's request
📊 STRATEGY: Selected approach and tool sequence  
🔍 EXECUTION_PLAN: Step-by-step execution plan
⚠️  RISK_ASSESSMENT: Potential risks and considerations
📋 EXPECTED_OUTCOMES: What results to expect

For tool execution, provide commands in this format:
COMMAND: tool_name arguments
DESCRIPTION: Brief explanation of what this command does
CATEGORY: Which ASM category this addresses

ETHICAL GUIDELINES:
- Only scan authorized targets
- Respect rate limits and avoid aggressive scanning
- Follow responsible disclosure practices
- Comply with local laws and regulations
- Prioritize stealth and minimal impact

Remember: You are an AI assistant that helps plan and analyze security testing. The human user will review and approve all commands before execution.`;
    }

    async handleUserInput(input) {
        if (!input) {
            this.rl.prompt();
            return;
        }

        // Handle built-in commands
        if (await this.handleBuiltinCommands(input)) {
            return;
        }

        // Process security testing request through AI
        await this.processSecurityRequest(input);
    }

    async handleBuiltinCommands(input) {
        const command = input.toLowerCase();
        
        switch (command) {
            case 'help':
                this.showHelp();
                return true;
                
            case 'status':
                this.showStatus();
                return true;
                
            case 'history':
                this.showHistory();
                return true;
                
            case 'clear':
                console.clear();
                this.showBanner();
                return true;
                
            case 'exit':
            case 'quit':
                this.shutdown();
                return true;
                
            default:
                return false;
        }
    }

    async processSecurityRequest(userInput) {
        try {
            this.printStatus('Processing your security testing request...');
            
            // Add user input to chat history
            this.chatHistory.push({
                role: 'user',
                parts: [{ text: userInput }]
            });

            // Get AI analysis and recommendations
            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(userInput);
            const response = await result.response;
            const aiResponse = response.text();

            // Add AI response to chat history
            this.chatHistory.push({
                role: 'model',
                parts: [{ text: aiResponse }]
            });

            // Parse and display AI response
            this.displayAIResponse(aiResponse);
            
            // Extract and handle any commands
            await this.handleAICommands(aiResponse, userInput);
            
        } catch (error) {
            this.printError(`AI processing failed: ${error.message}`);
            logger.error('Black-G AI processing error', { error: error.message, input: userInput });
        }
        
        this.rl.prompt();
    }

    displayAIResponse(response) {
        console.log(`\n${colors.magenta}🤖 Black-G Analysis:${colors.reset}\n`);
        
        // Parse structured response sections
        const sections = this.parseAIResponse(response);
        
        if (sections.objective) {
            console.log(`${colors.yellow}🎯 OBJECTIVE:${colors.reset} ${sections.objective}`);
        }
        
        if (sections.strategy) {
            console.log(`${colors.blue}📊 STRATEGY:${colors.reset} ${sections.strategy}`);
        }
        
        if (sections.execution_plan) {
            console.log(`${colors.green}🔍 EXECUTION PLAN:${colors.reset}\n${sections.execution_plan}`);
        }
        
        if (sections.risk_assessment) {
            console.log(`${colors.red}⚠️  RISK ASSESSMENT:${colors.reset} ${sections.risk_assessment}`);
        }
        
        if (sections.expected_outcomes) {
            console.log(`${colors.cyan}📋 EXPECTED OUTCOMES:${colors.reset} ${sections.expected_outcomes}`);
        }
        
        // Display any additional content
        if (sections.additional) {
            console.log(`\n${sections.additional}`);
        }
        
        console.log('');
    }

    parseAIResponse(response) {
        const sections = {};

        // Extract structured sections using regex patterns
        const patterns = {
            objective: /🎯\s*OBJECTIVE:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            strategy: /📊\s*STRATEGY:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            execution_plan: /🔍\s*EXECUTION[_\s]*PLAN:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            risk_assessment: /⚠️\s*RISK[_\s]*ASSESSMENT:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            expected_outcomes: /📋\s*EXPECTED[_\s]*OUTCOMES:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i
        };

        for (const [key, pattern] of Object.entries(patterns)) {
            const match = response.match(pattern);
            if (match) {
                sections[key] = match[1].trim();
            }
        }

        // Extract any remaining content as additional
        let remainingContent = response;
        for (const section of Object.values(sections)) {
            remainingContent = remainingContent.replace(section, '');
        }

        // Clean up remaining content
        remainingContent = remainingContent
            .replace(/🎯\s*OBJECTIVE:\s*/gi, '')
            .replace(/📊\s*STRATEGY:\s*/gi, '')
            .replace(/🔍\s*EXECUTION[_\s]*PLAN:\s*/gi, '')
            .replace(/⚠️\s*RISK[_\s]*ASSESSMENT:\s*/gi, '')
            .replace(/📋\s*EXPECTED[_\s]*OUTCOMES:\s*/gi, '')
            .trim();

        if (remainingContent) {
            sections.additional = remainingContent;
        }

        return sections;
    }

    async handleAICommands(aiResponse, originalInput) {
        // Extract commands from AI response
        const commands = this.extractCommands(aiResponse);

        if (commands.length === 0) {
            return;
        }

        console.log(`${colors.yellow}🔧 Detected ${commands.length} command(s) for execution:${colors.reset}\n`);

        for (let i = 0; i < commands.length; i++) {
            const cmd = commands[i];
            console.log(`${colors.white}${i + 1}. ${cmd.command}${colors.reset}`);
            if (cmd.description) {
                console.log(`   ${colors.blue}Description:${colors.reset} ${cmd.description}`);
            }
            if (cmd.category) {
                console.log(`   ${colors.green}ASM Category:${colors.reset} ${cmd.category}`);
            }
            console.log('');
        }

        // Ask for user confirmation
        const shouldExecute = await this.askConfirmation('Execute these commands?');

        if (shouldExecute) {
            await this.executeCommands(commands, originalInput);
        } else {
            this.printInfo('Command execution cancelled by user.');
        }
    }

    extractCommands(response) {
        const commands = [];
        const lines = response.split('\n');

        let currentCommand = null;

        for (const line of lines) {
            const trimmedLine = line.trim();

            // Look for COMMAND: pattern
            if (trimmedLine.startsWith('COMMAND:')) {
                if (currentCommand) {
                    commands.push(currentCommand);
                }
                currentCommand = {
                    command: trimmedLine.substring(8).trim(),
                    description: '',
                    category: ''
                };
            }
            // Look for DESCRIPTION: pattern
            else if (trimmedLine.startsWith('DESCRIPTION:') && currentCommand) {
                currentCommand.description = trimmedLine.substring(12).trim();
            }
            // Look for CATEGORY: pattern
            else if (trimmedLine.startsWith('CATEGORY:') && currentCommand) {
                currentCommand.category = trimmedLine.substring(9).trim();
            }
        }

        // Add the last command if exists
        if (currentCommand) {
            commands.push(currentCommand);
        }

        return commands;
    }

    async askConfirmation(question) {
        return new Promise((resolve) => {
            this.rl.question(`${colors.yellow}❓ ${question} (y/n): ${colors.reset}`, (answer) => {
                resolve(answer.toLowerCase().startsWith('y'));
            });
        });
    }

    async executeCommands(commands, originalInput) {
        this.activeScan = true;
        this.currentSession = {
            id: this.generateSessionId(),
            target: this.extractTarget(originalInput),
            startTime: new Date().toISOString(),
            commands: commands,
            results: []
        };

        this.printStatus(`Starting scan session: ${this.currentSession.id}`);

        for (let i = 0; i < commands.length; i++) {
            const cmd = commands[i];
            console.log(`\n${colors.cyan}[${i + 1}/${commands.length}] Executing: ${cmd.command}${colors.reset}`);

            try {
                const result = await this.executeCommand(cmd.command);
                this.currentSession.results.push({
                    command: cmd.command,
                    description: cmd.description,
                    category: cmd.category,
                    result: result,
                    timestamp: new Date().toISOString()
                });

                this.printSuccess(`Command completed successfully`);

                // Brief pause between commands
                await this.sleep(1000);

            } catch (error) {
                this.printError(`Command failed: ${error.message}`);
                this.currentSession.results.push({
                    command: cmd.command,
                    description: cmd.description,
                    category: cmd.category,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        this.activeScan = false;
        await this.finalizeScanSession();
    }

    async executeCommand(command) {
        return new Promise((resolve, reject) => {
            const parts = command.split(' ');
            const tool = parts[0];
            const args = parts.slice(1);

            let output = '';
            let errorOutput = '';

            const child = spawn(tool, args, {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            child.stdout.on('data', (data) => {
                const chunk = data.toString();
                process.stdout.write(chunk);
                output += chunk;
            });

            child.stderr.on('data', (data) => {
                const chunk = data.toString();
                process.stderr.write(chunk);
                errorOutput += chunk;
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve({
                        exitCode: code,
                        output: output,
                        errorOutput: errorOutput
                    });
                } else {
                    reject(new Error(`Command exited with code ${code}: ${errorOutput}`));
                }
            });

            child.on('error', (err) => {
                reject(new Error(`Failed to execute command: ${err.message}`));
            });
        });
    }

    async finalizeScanSession() {
        this.printStatus('Scan session completed. Analyzing results...');

        // Save session results
        await this.saveSessionResults();

        // Generate AI analysis of results
        await this.generateSessionAnalysis();

        // Offer to generate report
        const shouldGenerateReport = await this.askConfirmation('Generate professional report?');
        if (shouldGenerateReport) {
            await this.generateReport();
        }

        this.printSuccess(`Scan session ${this.currentSession.id} completed successfully`);
        this.currentSession = null;
    }

    async saveSessionResults() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `${timestamp}_${this.currentSession.id}_black-g-session.json`;
            const filepath = path.join('./scan_reports', filename);

            const sessionData = {
                ...this.currentSession,
                endTime: new Date().toISOString(),
                totalCommands: this.currentSession.commands.length,
                successfulCommands: this.currentSession.results.filter(r => !r.error).length,
                failedCommands: this.currentSession.results.filter(r => r.error).length
            };

            fs.writeFileSync(filepath, JSON.stringify(sessionData, null, 2));
            this.printInfo(`Session results saved to: ${filepath}`);

        } catch (error) {
            this.printError(`Failed to save session results: ${error.message}`);
        }
    }

    async generateSessionAnalysis() {
        try {
            const resultsText = this.formatResultsForAI();
            const analysisPrompt = `Analyze the following scan results and provide insights:

${resultsText}

Please provide:
1. Summary of findings
2. Risk assessment
3. Key vulnerabilities or issues discovered
4. Recommendations for next steps
5. Overall security posture assessment`;

            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(analysisPrompt);
            const response = await result.response;
            const analysis = response.text();

            console.log(`\n${colors.magenta}🧠 AI Analysis of Scan Results:${colors.reset}\n`);
            console.log(analysis);

            // Save analysis to session
            this.currentSession.aiAnalysis = analysis;

        } catch (error) {
            this.printError(`Failed to generate AI analysis: ${error.message}`);
        }
    }

    formatResultsForAI() {
        let resultsText = `Scan Session: ${this.currentSession.id}\n`;
        resultsText += `Target: ${this.currentSession.target}\n`;
        resultsText += `Commands Executed: ${this.currentSession.results.length}\n\n`;

        for (const result of this.currentSession.results) {
            resultsText += `Command: ${result.command}\n`;
            resultsText += `Category: ${result.category}\n`;
            if (result.error) {
                resultsText += `Status: FAILED - ${result.error}\n`;
            } else {
                resultsText += `Status: SUCCESS\n`;
                resultsText += `Output: ${result.result.output.substring(0, 1000)}...\n`;
            }
            resultsText += `---\n`;
        }

        return resultsText;
    }

    async generateReport() {
        try {
            this.printStatus('Generating professional report...');

            // Use existing report generation system
            const reportData = this.prepareReportData();
            await this.reporter.generateReport(reportData);

            this.printSuccess('Report generated successfully');

        } catch (error) {
            this.printError(`Report generation failed: ${error.message}`);
        }
    }

    prepareReportData() {
        return {
            sessionId: this.currentSession.id,
            target: this.currentSession.target,
            startTime: this.currentSession.startTime,
            endTime: new Date().toISOString(),
            commands: this.currentSession.commands,
            results: this.currentSession.results,
            aiAnalysis: this.currentSession.aiAnalysis || 'No AI analysis available'
        };
    }

    // Utility methods
    generateSessionId() {
        return 'bg-' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    extractTarget(input) {
        // Simple target extraction - can be enhanced
        const domainRegex = /(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g;
        const ipRegex = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}(?:\/[0-9]{1,2})?\b/g;

        const domainMatch = input.match(domainRegex);
        const ipMatch = input.match(ipRegex);

        return domainMatch?.[0] || ipMatch?.[0] || 'unknown';
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Display methods
    printStatus(message) {
        console.log(`${colors.green}[+]${colors.reset} ${message}`);
    }

    printError(message) {
        console.log(`${colors.red}[-]${colors.reset} ${message}`);
    }

    printWarning(message) {
        console.log(`${colors.yellow}[!]${colors.reset} ${message}`);
    }

    printInfo(message) {
        console.log(`${colors.blue}[i]${colors.reset} ${message}`);
    }

    printSuccess(message) {
        console.log(`${colors.green}[✓]${colors.reset} ${message}`);
    }

    showHelp() {
        console.log(`\n${colors.cyan}Black-G CLI Help${colors.reset}\n`);
        console.log(`${colors.yellow}Natural Language Commands:${colors.reset}`);
        console.log('  • "Perform passive reconnaissance on example.com"');
        console.log('  • "Run comprehensive ASM analysis on target.com"');
        console.log('  • "Check SSL vulnerabilities on my web servers"');
        console.log('  • "Scan for open ports on 192.168.1.0/24"');
        console.log('  • "Find subdomains for company.com"');
        console.log('  • "Analyze attack surface of my application"');

        console.log(`\n${colors.yellow}Built-in Commands:${colors.reset}`);
        console.log('  help     - Show this help message');
        console.log('  status   - Show current system status');
        console.log('  history  - Show command history');
        console.log('  clear    - Clear screen and show banner');
        console.log('  exit     - Exit Black-G CLI');

        console.log(`\n${colors.yellow}ASM Categories:${colors.reset}`);
        console.log('  1. Domain/IP Vulnerabilities');
        console.log('  2. SSL/TLS Certificate Analysis');
        console.log('  3. Configuration Issues');
        console.log('  4. Open Ports & Services');
        console.log('  5. IP/Domain Reputation');
        console.log('  6. Cloud Security Assessment');
        console.log('  7. Authentication Discovery');

        console.log(`\n${colors.blue}Tips:${colors.reset}`);
        console.log('  • Be specific about your target and objectives');
        console.log('  • Black-G will ask for confirmation before executing commands');
        console.log('  • All scan results are saved and can be used for reporting');
        console.log('  • Use natural language - Black-G understands context');
        console.log('');
    }

    showStatus() {
        console.log(`\n${colors.cyan}Black-G System Status${colors.reset}\n`);
        console.log(`${colors.yellow}AI Engine:${colors.reset} ${this.model ? 'Connected' : 'Disconnected'}`);
        console.log(`${colors.yellow}Active Scan:${colors.reset} ${this.activeScan ? 'Yes' : 'No'}`);

        if (this.currentSession) {
            console.log(`${colors.yellow}Current Session:${colors.reset} ${this.currentSession.id}`);
            console.log(`${colors.yellow}Target:${colors.reset} ${this.currentSession.target}`);
            console.log(`${colors.yellow}Commands Executed:${colors.reset} ${this.currentSession.results.length}/${this.currentSession.commands.length}`);
        }

        console.log(`${colors.yellow}Chat History:${colors.reset} ${this.chatHistory.length} messages`);

        // Check tool availability
        console.log(`\n${colors.cyan}Tool Availability:${colors.reset}`);
        const tools = ['nmap', 'subfinder', 'nuclei', 'amass'];
        tools.forEach(tool => {
            // This is a simplified check - in production, you'd actually test tool availability
            console.log(`${colors.yellow}${tool}:${colors.reset} Available`);
        });

        console.log('');
    }

    showHistory() {
        console.log(`\n${colors.cyan}Command History${colors.reset}\n`);

        const userMessages = this.chatHistory.filter(msg => msg.role === 'user');

        if (userMessages.length <= 1) { // First message is system prompt
            console.log('No command history available.');
        } else {
            userMessages.slice(1).forEach((msg, index) => {
                console.log(`${colors.yellow}${index + 1}.${colors.reset} ${msg.parts[0].text}`);
            });
        }

        console.log('');
    }

    shutdown() {
        console.log(`\n${colors.yellow}🛑 Shutting down Black-G CLI...${colors.reset}`);

        if (this.activeScan) {
            console.log(`${colors.red}⚠️  Warning: Active scan in progress will be terminated.${colors.reset}`);
        }

        console.log(`${colors.green}Thank you for using Black-G CLI!${colors.reset}`);
        console.log(`${colors.blue}Stay secure! 🛡️${colors.reset}\n`);

        process.exit(0);
    }
}

// Supporting Classes

class ToolSelector {
    constructor() {
        this.toolMap = {
            'passive': ['subfinder', 'amass', 'whois', 'dig'],
            'asm_full': ['subfinder', 'nmap', 'nuclei', 'sslscan'],
            'vulnerability': ['nmap', 'nuclei'],
            'ssl_analysis': ['sslscan', 'testssl'],
            'port_scan': ['nmap', 'masscan'],
            'subdomain': ['subfinder', 'amass'],
            'web_scan': ['gobuster', 'nuclei']
        };
    }

    analyzeRequest(userInput) {
        const intent = this.parseIntent(userInput);
        const target = this.extractTarget(userInput);
        const scanType = this.determineScanType(intent);

        return {
            intent,
            target,
            scanType,
            tools: this.selectTools(scanType, target),
            strategy: this.buildStrategy(scanType, target)
        };
    }

    parseIntent(input) {
        const lowerInput = input.toLowerCase();

        if (lowerInput.includes('passive') || lowerInput.includes('reconnaissance')) {
            return 'passive_recon';
        } else if (lowerInput.includes('asm') || lowerInput.includes('attack surface')) {
            return 'asm_analysis';
        } else if (lowerInput.includes('ssl') || lowerInput.includes('certificate')) {
            return 'ssl_analysis';
        } else if (lowerInput.includes('port') || lowerInput.includes('scan')) {
            return 'port_scan';
        } else if (lowerInput.includes('subdomain')) {
            return 'subdomain_enum';
        } else if (lowerInput.includes('vulnerability') || lowerInput.includes('vuln')) {
            return 'vulnerability_scan';
        }

        return 'general_scan';
    }

    determineScanType(intent) {
        const scanTypeMap = {
            'passive_recon': 'passive',
            'asm_analysis': 'asm_full',
            'ssl_analysis': 'ssl_analysis',
            'port_scan': 'port_scan',
            'subdomain_enum': 'subdomain',
            'vulnerability_scan': 'vulnerability',
            'general_scan': 'asm_full'
        };

        return scanTypeMap[intent] || 'asm_full';
    }

    selectTools(scanType, target) {
        return this.toolMap[scanType] || this.toolMap['asm_full'];
    }

    buildStrategy(scanType, target) {
        const strategies = {
            'passive': 'Passive reconnaissance without direct target interaction',
            'asm_full': 'Comprehensive attack surface analysis across all categories',
            'ssl_analysis': 'SSL/TLS certificate and configuration analysis',
            'port_scan': 'Port scanning and service enumeration',
            'subdomain': 'Subdomain discovery and enumeration',
            'vulnerability': 'Vulnerability assessment and scanning'
        };

        return strategies[scanType] || 'General security assessment';
    }

    extractTarget(input) {
        const domainRegex = /(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g;
        const ipRegex = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}(?:\/[0-9]{1,2})?\b/g;

        const domainMatch = input.match(domainRegex);
        const ipMatch = input.match(ipRegex);

        return domainMatch?.[0] || ipMatch?.[0] || 'unknown';
    }
}

class ASMAnalyzer {
    constructor() {
        this.categories = [
            'Domain/IP Vulnerabilities',
            'SSL/TLS Certificate Analysis',
            'Configuration Issues',
            'Open Ports & Services',
            'IP/Domain Reputation',
            'Cloud Security Assessment',
            'Authentication Discovery'
        ];
    }

    analyzeResults(results) {
        const analysis = {
            categories: {},
            riskLevel: 'UNKNOWN',
            findings: [],
            recommendations: []
        };

        for (const result of results) {
            if (result.category) {
                if (!analysis.categories[result.category]) {
                    analysis.categories[result.category] = [];
                }
                analysis.categories[result.category].push(result);
            }
        }

        // Simple risk assessment based on results
        analysis.riskLevel = this.assessRisk(results);

        return analysis;
    }

    assessRisk(results) {
        const failedCommands = results.filter(r => r.error).length;
        const totalCommands = results.length;

        if (failedCommands > totalCommands / 2) {
            return 'HIGH';
        } else if (failedCommands > 0) {
            return 'MEDIUM';
        } else {
            return 'LOW';
        }
    }
}

class BlackGReporter {
    constructor() {
        this.reportsDir = './scan_reports';
        this.templatesDir = './report template';
    }

    async generateReport(reportData) {
        try {
            // Generate multiple formats
            await this.generateJSONReport(reportData);
            await this.generateMarkdownReport(reportData);

            // Try to generate DOCX if template exists
            if (fs.existsSync(path.join(this.templatesDir, 'template report.docx'))) {
                await this.generateDOCXReport(reportData);
            }

        } catch (error) {
            throw new Error(`Report generation failed: ${error.message}`);
        }
    }

    async generateJSONReport(data) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_black-g-report.json`;
        const filepath = path.join(this.reportsDir, filename);

        const reportData = {
            metadata: {
                generator: 'Black-G CLI',
                version: '1.0.0',
                generatedAt: new Date().toISOString(),
                sessionId: data.sessionId,
                target: data.target
            },
            scanDetails: {
                startTime: data.startTime,
                endTime: data.endTime,
                totalCommands: data.commands.length,
                successfulCommands: data.results.filter(r => !r.error).length,
                failedCommands: data.results.filter(r => r.error).length
            },
            results: data.results,
            aiAnalysis: data.aiAnalysis
        };

        fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
        console.log(`${colors.green}[✓]${colors.reset} JSON report saved: ${filepath}`);
    }

    async generateMarkdownReport(data) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_black-g-report.md`;
        const filepath = path.join(this.reportsDir, filename);

        const markdown = `# Black-G Security Assessment Report

**Target:** ${data.target}
**Session ID:** ${data.sessionId}
**Generated:** ${new Date().toLocaleString()}
**Duration:** ${data.startTime} - ${data.endTime}

## Executive Summary

This report contains the results of a security assessment performed using Black-G CLI, an AI-driven penetration testing system specialized in Attack Surface Management.

## Scan Details

- **Total Commands Executed:** ${data.commands.length}
- **Successful Commands:** ${data.results.filter(r => !r.error).length}
- **Failed Commands:** ${data.results.filter(r => r.error).length}

## Command Results

${data.results.map((result, index) => `
### ${index + 1}. ${result.command}

**Category:** ${result.category || 'General'}
**Status:** ${result.error ? 'FAILED' : 'SUCCESS'}
**Timestamp:** ${result.timestamp}

${result.error ? `**Error:** ${result.error}` : '**Output:** Command executed successfully'}

---
`).join('')}

## AI Analysis

${data.aiAnalysis}

---

*Report generated by Black-G CLI v1.0.0*
*© 2025 - Automated Security Assessment*
`;

        fs.writeFileSync(filepath, markdown);
        console.log(`${colors.green}[✓]${colors.reset} Markdown report saved: ${filepath}`);
    }

    async generateDOCXReport(data) {
        // This would integrate with the existing DOCX generation system
        console.log(`${colors.blue}[i]${colors.reset} DOCX report generation would integrate with existing template system`);
    }
}

// Main execution
if (require.main === module) {
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log(`\n${colors.yellow}🛑 Received interrupt signal. Shutting down gracefully...${colors.reset}`);
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log(`\n${colors.yellow}🛑 Received termination signal. Shutting down gracefully...${colors.reset}`);
        process.exit(0);
    });

    // Start Black-G CLI
    try {
        new BlackGCLI();
    } catch (error) {
        console.error(`${colors.red}[-] Failed to start Black-G CLI: ${error.message}${colors.reset}`);
        process.exit(1);
    }
}
