#!/bin/bash

# Interactive ASM System Startup Script
# This script provides multiple ways to run the enhanced ASM system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ASCII Art Banner
show_banner() {
    echo -e "${CYAN}"
    cat << "EOF"
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🤖 INTERACTIVE AI ATTACK SURFACE MANAGEMENT SYSTEM       ║
    ║                                                              ║
    ║    Powered by Google Gemini AI                               ║
    ║    Enhanced Intelligence • Real-time Analysis               ║
    ║    Web Dashboard • Autonomous Scanning                       ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Function to print colored output
print_status() {
    echo -e "${GREEN}[+]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[-]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[★]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        print_info "Please install Node.js: https://nodejs.org/"
        exit 1
    fi
    print_status "Node.js: $(node --version)"
    
    # Check npm packages
    if [[ ! -d "node_modules" ]]; then
        print_warning "Node modules not found. Installing dependencies..."
        npm install
    fi
    
    # Check environment variables
    if [[ -z "$GEMINI_API_KEY" ]]; then
        if [[ ! -f ".env" ]]; then
            print_error "GEMINI_API_KEY not set and .env file not found"
            print_info "Please create a .env file with: GEMINI_API_KEY=your_api_key_here"
            exit 1
        fi
    fi
    print_status "Environment configuration: OK"
    
    # Check ASM tools
    local tools=("nmap" "subfinder" "nuclei")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            print_status "$tool: Available"
        else
            print_warning "$tool: Not found (some features may be limited)"
        fi
    done
    
    print_status "Prerequisites check completed"
    echo ""
}

# Function to show usage
show_usage() {
    echo -e "${CYAN}Interactive ASM System - Usage Options:${NC}"
    echo ""
    echo "🎯 Available Modes:"
    echo "  1. web       - Launch Web Dashboard (Recommended)"
    echo "  2. cli       - Enhanced Command Line Interface"
    echo "  3. classic   - Original CLI Interface"
    echo "  4. black-g   - Black-G AI Penetration Testing CLI"
    echo "  5. auto      - Auto-detect best mode"
    echo ""
    echo "🚀 Quick Start:"
    echo "  $0 web       # Start web dashboard on http://localhost:3000"
    echo "  $0 cli       # Start enhanced CLI with AI intelligence"
    echo "  $0 classic   # Start original command-line interface"
    echo "  $0 black-g   # Start Black-G AI penetration testing CLI"
    echo ""
    echo "⚙️  Options:"
    echo "  --port PORT  # Specify port for web dashboard (default: 3000)"
    echo "  --help       # Show this help message"
    echo ""
    echo "📚 Examples:"
    echo "  $0 web --port 8080    # Start web dashboard on port 8080"
    echo "  $0 cli                # Start enhanced CLI mode"
    echo "  $0 black-g            # Start Black-G penetration testing CLI"
    echo ""
}

# Function to start web dashboard
start_web_dashboard() {
    local port=${1:-3000}

    print_header "Starting Enhanced Interactive Web Dashboard..."
    print_info "Port: $port"
    print_info "Access URL: http://localhost:$port"
    print_info "Features: Modular architecture, enhanced security, improved AI integration"
    print_info "Press Ctrl+C to stop"
    echo ""

    export PORT=$port
    node server.js
}

# Function to start enhanced CLI
start_enhanced_cli() {
    print_header "Starting Enhanced AI CLI Interface..."
    print_info "Features: Intelligent analysis, real-time risk assessment, adaptive scanning"
    print_info "Type 'exit' to quit"
    echo ""
    
    node enhanced_ai_asm.js
}

# Function to start classic CLI
start_classic_cli() {
    print_header "Starting Classic CLI Interface..."
    print_info "Original command-line interface with basic AI integration"
    print_info "Type 'exit' to quit"
    echo ""

    node interactive.js
}

# Function to start Black-G CLI
start_black_g_cli() {
    print_header "Starting Black-G AI Penetration Testing CLI..."
    print_info "Natural language driven Attack Surface Management"
    print_info "Features: AI-powered tool selection, comprehensive ASM analysis, professional reporting"
    print_info "Compatible with PuTTY/SSH connections"
    print_info "Type 'exit' to quit"
    echo ""

    node black-g-cli.js
}

# Function to auto-detect best mode
auto_detect_mode() {
    print_header "Auto-detecting best mode..."
    
    # Check if running in a desktop environment
    if [[ -n "$DISPLAY" ]] || [[ -n "$WAYLAND_DISPLAY" ]] || [[ "$TERM_PROGRAM" == "vscode" ]]; then
        print_info "Desktop environment detected - starting web dashboard"
        start_web_dashboard
    else
        print_info "Terminal environment detected - starting enhanced CLI"
        start_enhanced_cli
    fi
}

# Function to show system status
show_status() {
    print_header "System Status"
    echo ""
    
    # Check if web dashboard is running
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status "Web Dashboard: Running on port 3000"
    else
        print_info "Web Dashboard: Not running"
    fi
    
    # Check active scan sessions
    local scan_count=$(ls scan_reports/*.log 2>/dev/null | wc -l)
    print_info "Scan Reports: $scan_count files"
    
    # Check system resources
    print_info "System Load: $(uptime | awk -F'load average:' '{print $2}')"
    print_info "Memory Usage: $(free -h | awk '/^Mem:/ {print $3 "/" $2}')"
    
    echo ""
}

# Function to clean up old files
cleanup() {
    print_header "Cleaning up old files..."
    
    # Clean old log files (older than 7 days)
    find scan_reports/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # Clean temporary files
    rm -rf temp/* 2>/dev/null || true
    
    print_status "Cleanup completed"
}

# Function to update system
update_system() {
    print_header "Updating ASM System..."
    
    # Update npm packages
    print_info "Updating Node.js dependencies..."
    npm update
    
    # Update nuclei templates if nuclei is available
    if command -v nuclei &> /dev/null; then
        print_info "Updating Nuclei templates..."
        nuclei -update-templates
    fi
    
    print_status "System updated successfully"
}

# Main script logic
main() {
    show_banner
    
    # Parse command line arguments
    local mode=""
    local port=3000
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            web|cli|classic|black-g|auto)
                mode="$1"
                shift
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            --status)
                show_status
                exit 0
                ;;
            --cleanup)
                cleanup
                exit 0
                ;;
            --update)
                update_system
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Default to auto mode if no mode specified
    if [[ -z "$mode" ]]; then
        mode="auto"
    fi
    
    # Check prerequisites
    check_prerequisites
    
    # Start the selected mode
    case "$mode" in
        "web")
            start_web_dashboard "$port"
            ;;
        "cli")
            start_enhanced_cli
            ;;
        "classic")
            start_classic_cli
            ;;
        "black-g")
            start_black_g_cli
            ;;
        "auto")
            auto_detect_mode
            ;;
        *)
            print_error "Invalid mode: $mode"
            show_usage
            exit 1
            ;;
    esac
}

# Handle Ctrl+C gracefully
trap 'echo -e "\n${YELLOW}[!]${NC} Shutting down gracefully..."; exit 0' INT

# Run main function
main "$@"
