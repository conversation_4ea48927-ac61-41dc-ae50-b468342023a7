/**
 * Scan Controller
 * Handles all scan-related API endpoints
 */

const scanService = require('../../services/scanService');
const aiService = require('../../services/aiService');
const logger = require('../../utils/logger');
const { asyncHandler, ValidationError, NotFoundError } = require('../../middleware/errorHandler');

/**
 * Start a new scan session
 * POST /api/scan/start
 */
const startScan = asyncHandler(async (req, res) => {
    const { target, scanType = 'auto', options = {} } = req.body;

    if (!target) {
        throw new ValidationError('Target is required');
    }

    // Create new scan session
    const session = scanService.createSession(target, scanType, options);

    // Get AI analysis for the target
    const aiAnalysis = await aiService.analyzeTarget(target, scanType);
    
    // Update session with AI analysis
    session.aiAnalysis = aiAnalysis;
    session.status = 'analyzing';
    scanService.updateSession(session.id, session);

    logger.info('Scan session started', {
        sessionId: session.id,
        target,
        scanType,
        aiRiskLevel: aiAnalysis.riskLevel
    });

    res.status(201).json({
        success: true,
        data: {
            sessionId: session.id,
            target: session.target,
            scanType: session.scanType,
            status: session.status,
            aiAnalysis: {
                riskLevel: aiAnalysis.riskLevel,
                analysis: aiAnalysis.analysis,
                nextAction: aiAnalysis.nextAction
            }
        },
        message: 'Scan session created successfully'
    });
});

/**
 * Get scan session details
 * GET /api/scan/:sessionId
 */
const getScanSession = asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const session = scanService.getSession(sessionId);

    if (!session) {
        throw new NotFoundError('Scan session not found');
    }

    res.json({
        success: true,
        data: session
    });
});

/**
 * Execute a command in a scan session
 * POST /api/scan/:sessionId/execute
 */
const executeCommand = asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { command, tool = 'manual' } = req.body;

    if (!command) {
        throw new ValidationError('Command is required');
    }

    const session = scanService.getSession(sessionId);
    if (!session) {
        throw new NotFoundError('Scan session not found');
    }

    // Execute the command
    const result = await scanService.executeCommand(sessionId, command, tool);

    // Get AI analysis of the command output
    const aiAnalysis = await aiService.processCommandOutput(
        command, 
        result.output, 
        { sessionId, target: session.target, findings: session.findings }
    );

    // Update session with AI analysis
    session.lastAiAnalysis = aiAnalysis;
    scanService.updateSession(sessionId, session);

    res.json({
        success: true,
        data: {
            result,
            aiAnalysis: {
                analysis: aiAnalysis.analysis,
                riskLevel: aiAnalysis.riskLevel,
                nextAction: aiAnalysis.nextAction,
                command: aiAnalysis.command
            }
        }
    });
});

/**
 * Run automated scan phases
 * POST /api/scan/:sessionId/auto
 */
const runAutomatedScan = asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { phase = 'reconnaissance' } = req.body;

    const session = scanService.getSession(sessionId);
    if (!session) {
        throw new NotFoundError('Scan session not found');
    }

    let results = [];

    try {
        session.status = 'scanning';
        scanService.updateSession(sessionId, session);

        switch (phase) {
            case 'reconnaissance':
                results = await scanService.runSubdomainEnumeration(sessionId, session.target);
                break;
            
            case 'port_scan':
                results = await scanService.runPortScan(sessionId, session.target);
                break;
            
            case 'vulnerability_assessment':
                results = await scanService.runVulnerabilityAssessment(sessionId, session.target);
                break;
            
            default:
                throw new ValidationError('Invalid scan phase');
        }

        session.status = 'completed';
        scanService.updateSession(sessionId, session);

        // Get AI analysis of the scan results
        const aiAnalysis = await aiService.generateScanStrategy(session.target, session.findings);

        res.json({
            success: true,
            data: {
                phase,
                results,
                findings: session.findings,
                aiAnalysis: {
                    analysis: aiAnalysis.analysis,
                    riskLevel: aiAnalysis.riskLevel,
                    nextAction: aiAnalysis.nextAction
                }
            }
        });

    } catch (error) {
        session.status = 'error';
        session.error = error.message;
        scanService.updateSession(sessionId, session);
        throw error;
    }
});

/**
 * Get all active scan sessions
 * GET /api/scan/sessions
 */
const getAllSessions = asyncHandler(async (req, res) => {
    const sessions = scanService.getAllSessions();
    
    // Return summary information only
    const sessionSummaries = sessions.map(session => ({
        id: session.id,
        target: session.target,
        scanType: session.scanType,
        status: session.status,
        startTime: session.startTime,
        findingsCount: {
            subdomains: session.findings.subdomains.length,
            openPorts: session.findings.openPorts.length,
            vulnerabilities: session.findings.vulnerabilities.length
        },
        riskLevel: session.findings.riskLevel
    }));

    res.json({
        success: true,
        data: sessionSummaries,
        count: sessionSummaries.length
    });
});

/**
 * Delete a scan session
 * DELETE /api/scan/:sessionId
 */
const deleteScanSession = asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    
    const deleted = scanService.deleteSession(sessionId);
    
    if (!deleted) {
        throw new NotFoundError('Scan session not found');
    }

    res.json({
        success: true,
        message: 'Scan session deleted successfully'
    });
});

/**
 * Get scan session findings
 * GET /api/scan/:sessionId/findings
 */
const getScanFindings = asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const session = scanService.getSession(sessionId);

    if (!session) {
        throw new NotFoundError('Scan session not found');
    }

    // Get AI risk assessment
    const riskAssessment = await aiService.assessRisk(session.findings);

    res.json({
        success: true,
        data: {
            sessionId,
            target: session.target,
            findings: session.findings,
            riskAssessment: {
                analysis: riskAssessment.analysis,
                riskLevel: riskAssessment.riskLevel,
                recommendations: riskAssessment.nextAction
            },
            scanHistory: session.commands.map(cmd => ({
                tool: cmd.tool,
                command: cmd.command,
                timestamp: cmd.timestamp,
                exitCode: cmd.exitCode
            }))
        }
    });
});

/**
 * Update scan session status
 * PATCH /api/scan/:sessionId/status
 */
const updateScanStatus = asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { status } = req.body;

    const validStatuses = ['initializing', 'analyzing', 'scanning', 'completed', 'error', 'cancelled'];
    
    if (!validStatuses.includes(status)) {
        throw new ValidationError('Invalid status value');
    }

    const session = scanService.getSession(sessionId);
    if (!session) {
        throw new NotFoundError('Scan session not found');
    }

    session.status = status;
    if (status === 'completed') {
        session.endTime = new Date().toISOString();
    }

    scanService.updateSession(sessionId, session);

    res.json({
        success: true,
        data: {
            sessionId,
            status: session.status,
            endTime: session.endTime
        }
    });
});

module.exports = {
    startScan,
    getScanSession,
    executeCommand,
    runAutomatedScan,
    getAllSessions,
    deleteScanSession,
    getScanFindings,
    updateScanStatus
};
