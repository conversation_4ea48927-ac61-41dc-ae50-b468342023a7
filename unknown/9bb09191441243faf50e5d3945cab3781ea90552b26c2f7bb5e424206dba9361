<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced ASM Dashboard - Interactive AI Attack Surface Management</title>
    <script src="/socket.io/socket.io.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --border-color: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 24px;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 8px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            font-weight: 400;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 24px;
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .panel {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
        }

        .panel h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 12px;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(102, 126, 234, 0.1) 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .metric:hover {
            transform: scale(1.05);
            border-color: var(--primary-color);
        }

        .metric-value {
            font-size: 2.2em;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 0.9em;
            color: var(--text-secondary);
            font-weight: 500;
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 450px;
            min-height: 450px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background: rgba(15, 23, 42, 0.5);
            border-radius: 12px;
            margin-bottom: 16px;
            min-height: 300px;
            border: 1px solid var(--border-color);
        }

        .message {
            margin-bottom: 16px;
            padding: 16px;
            border-radius: 12px;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }

        .message.user {
            background: linear-gradient(135deg, var(--info-color), #1e40af);
            margin-left: 40px;
            border-bottom-right-radius: 4px;
        }

        .message.ai {
            background: linear-gradient(135deg, var(--success-color), #059669);
            margin-right: 40px;
            border-bottom-left-radius: 4px;
        }

        .message.system {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            font-style: italic;
            text-align: center;
            margin: 0 20px;
        }

        .input-group {
            display: flex;
            gap: 12px;
            flex-shrink: 0;
        }

        .input-group input {
            flex: 1;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--card-bg);
            color: var(--text-primary);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn-primary { background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); }
        .btn-success { background: linear-gradient(135deg, var(--success-color), #059669); }
        .btn-warning { background: linear-gradient(135deg, var(--warning-color), #d97706); }
        .btn-danger { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

        .terminal {
            background: #000;
            color: #00ff00;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            padding: 20px;
            border-radius: 12px;
            height: 350px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            font-size: 13px;
            line-height: 1.4;
        }

        .risk-critical { color: var(--danger-color); font-weight: 700; }
        .risk-high { color: #ff6b6b; font-weight: 600; }
        .risk-medium { color: var(--warning-color); font-weight: 500; }
        .risk-low { color: var(--success-color); font-weight: 500; }
        .risk-unknown { color: var(--text-muted); }

        .findings-list {
            max-height: 250px;
            overflow-y: auto;
            padding-right: 8px;
        }

        .finding-item {
            background: rgba(102, 126, 234, 0.1);
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            font-size: 0.9em;
            border-left: 4px solid var(--primary-color);
            transition: all 0.2s ease;
        }

        .finding-item:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateX(4px);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        .status-running { background: var(--warning-color); }
        .status-completed { background: var(--success-color); }
        .status-error { background: var(--danger-color); }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-top: 20px;
        }

        .target-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 16px;
            border-radius: 12px;
            margin: 16px 0;
            border: 1px solid var(--border-color);
        }

        .session-status {
            background: var(--card-bg);
            padding: 16px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-robot"></i> Enhanced ASM Dashboard</h1>
        <p>AI-Powered Interactive Attack Surface Management System</p>
    </div>

    <div class="container">
        <div class="panel">
            <h2><i class="fas fa-chart-line"></i> Real-time Metrics</h2>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="subdomains-count">0</div>
                    <div class="metric-label"><i class="fas fa-globe"></i> Subdomains</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="ports-count">0</div>
                    <div class="metric-label"><i class="fas fa-door-open"></i> Open Ports</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="vulns-count">0</div>
                    <div class="metric-label"><i class="fas fa-bug"></i> Vulnerabilities</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="risk-level">UNKNOWN</div>
                    <div class="metric-label"><i class="fas fa-shield-alt"></i> Risk Level</div>
                </div>
            </div>

            <h3><i class="fas fa-crosshairs"></i> Current Target</h3>
            <div id="current-target" class="target-info">
                <i class="fas fa-info-circle"></i> No active session - Start a new scan to begin
            </div>

            <h3><i class="fas fa-search"></i> Recent Findings</h3>
            <div id="findings-list" class="findings-list">
                <div class="finding-item">
                    <i class="fas fa-play-circle"></i> Start a new scan to see findings...
                </div>
            </div>
        </div>

        <div class="panel">
            <h2><i class="fas fa-comments"></i> AI Assistant</h2>
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <div class="message system">
                        <i class="fas fa-robot"></i> Enhanced AI Assistant ready. Start a new scan or ask questions about ASM techniques.
                    </div>
                </div>
                <div class="input-group">
                    <input type="text" id="message-input" placeholder="Enter target domain or ask AI assistant..." autocomplete="off" />
                    <button onclick="sendMessage()" type="button" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send
                    </button>
                    <button onclick="startNewScan()" type="button" class="btn btn-success">
                        <i class="fas fa-play"></i> New Scan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="panel">
            <h2><i class="fas fa-terminal"></i> Live Terminal Output</h2>
            <div id="terminal" class="terminal">
                <span style="color: #00ff00;">╔══════════════════════════════════════════════════════════════╗</span>
                <span style="color: #00ff00;">║                                                              ║</span>
                <span style="color: #00ff00;">║  🤖 Enhanced Interactive ASM System                          ║</span>
                <span style="color: #00ff00;">║                                                              ║</span>
                <span style="color: #00ff00;">║  Ready to execute commands...                                ║</span>
                <span style="color: #00ff00;">║                                                              ║</span>
                <span style="color: #00ff00;">╚══════════════════════════════════════════════════════════════╝</span>
            </div>
        </div>

        <div class="panel">
            <h2><i class="fas fa-chart-bar"></i> Session Status</h2>
            <div id="session-status" class="session-status">
                <span class="status-indicator status-completed"></span>
                <strong>System Ready</strong> - No active scans
            </div>

            <h3 style="margin-top: 24px;"><i class="fas fa-tools"></i> Quick Actions</h3>
            <div class="quick-actions">
                <button onclick="startPassiveScan()" class="btn btn-success">
                    <i class="fas fa-eye"></i> Passive Scan
                </button>
                <button onclick="startActiveScan()" class="btn btn-warning">
                    <i class="fas fa-crosshairs"></i> Active Scan
                </button>
                <button onclick="generateReport()" class="btn btn-primary">
                    <i class="fas fa-file-alt"></i> Generate Report
                </button>
                <button onclick="clearSession()" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Clear Session
                </button>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let currentSessionId = null;

        // Socket event handlers
        socket.on('session-update', (session) => {
            updateDashboard(session);
        });

        socket.on('command-output', (data) => {
            appendToTerminal(data.data);
        });

        socket.on('command-complete', (data) => {
            appendToTerminal(`\n✅ Command completed: ${data.command}\n`);
        });

        // Dashboard functions
        function updateDashboard(session) {
            currentSessionId = session.id;

            // Update metrics with animations
            updateMetric('subdomains-count', session.findings.subdomains.length);
            updateMetric('ports-count', session.findings.openPorts.length);
            updateMetric('vulns-count', session.findings.vulnerabilities.length);

            const riskElement = document.getElementById('risk-level');
            riskElement.textContent = session.findings.riskLevel;
            riskElement.className = `metric-value risk-${session.findings.riskLevel.toLowerCase()}`;

            // Update target with icon
            const targetElement = document.getElementById('current-target');
            targetElement.innerHTML = `<i class="fas fa-bullseye"></i> <strong>Target:</strong> ${session.target}`;

            // Update chat
            updateChat(session.chatHistory);

            // Update findings
            updateFindings(session.findings);

            // Update session status
            updateSessionStatus(session);
        }

        function updateMetric(elementId, newValue) {
            const element = document.getElementById(elementId);
            const currentValue = parseInt(element.textContent) || 0;

            if (newValue !== currentValue) {
                element.style.transform = 'scale(1.1)';
                element.style.color = 'var(--success-color)';

                setTimeout(() => {
                    element.textContent = newValue;
                    element.style.transform = 'scale(1)';
                    element.style.color = 'var(--primary-color)';
                }, 200);
            }
        }

        function updateChat(chatHistory) {
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = '';

            chatHistory.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.type}`;

                const icon = msg.type === 'user' ? '<i class="fas fa-user"></i>' :
                           msg.type === 'ai' ? '<i class="fas fa-robot"></i>' :
                           '<i class="fas fa-cog"></i>';

                const title = msg.type === 'user' ? 'You' :
                            msg.type === 'ai' ? 'AI Assistant' : 'System';

                messageDiv.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        ${icon} <strong>${title}</strong>
                        <span style="font-size: 0.8em; color: var(--text-muted); margin-left: auto;">
                            ${new Date(msg.timestamp || Date.now()).toLocaleTimeString()}
                        </span>
                    </div>
                    <div>${msg.message}</div>
                    ${msg.command ? `
                        <div style="background: rgba(0,0,0,0.3); padding: 8px; margin-top: 8px; border-radius: 6px; font-family: monospace; border-left: 3px solid var(--primary-color);">
                            <i class="fas fa-terminal"></i> ${msg.command}
                        </div>
                    ` : ''}
                `;
                chatMessages.appendChild(messageDiv);
            });

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function updateFindings(findings) {
            const findingsList = document.getElementById('findings-list');
            findingsList.innerHTML = '';

            // Show subdomains
            findings.subdomains.slice(-5).forEach(sub => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                item.innerHTML = `<i class="fas fa-globe"></i> <strong>Subdomain:</strong> ${sub}`;
                findingsList.appendChild(item);
            });

            // Show open ports
            findings.openPorts.slice(-5).forEach(port => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                const portInfo = typeof port === 'object' ? `${port.port}/${port.protocol} (${port.service})` : port;
                item.innerHTML = `<i class="fas fa-door-open"></i> <strong>Open Port:</strong> ${portInfo}`;
                findingsList.appendChild(item);
            });

            // Show vulnerabilities
            findings.vulnerabilities.slice(-3).forEach(vuln => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                const severity = vuln.severity || 'unknown';
                const severityColor = {
                    'critical': 'var(--danger-color)',
                    'high': '#ff6b6b',
                    'medium': 'var(--warning-color)',
                    'low': 'var(--success-color)'
                }[severity] || 'var(--text-muted)';

                item.innerHTML = `
                    <i class="fas fa-bug"></i> <strong>Vulnerability:</strong> ${vuln.name || vuln.id}
                    <span style="color: ${severityColor}; font-weight: bold; margin-left: 8px;">[${severity.toUpperCase()}]</span>
                `;
                findingsList.appendChild(item);
            });

            if (findingsList.children.length === 0) {
                const item = document.createElement('div');
                item.className = 'finding-item';
                item.innerHTML = '<i class="fas fa-info-circle"></i> No findings yet - scan in progress...';
                findingsList.appendChild(item);
            }
        }

        function updateSessionStatus(session) {
            const statusElement = document.getElementById('session-status');
            const statusMap = {
                'initializing': { icon: 'fas fa-spinner fa-spin', color: 'var(--info-color)', text: 'Initializing' },
                'analyzing': { icon: 'fas fa-brain', color: 'var(--primary-color)', text: 'AI Analyzing' },
                'scanning': { icon: 'fas fa-radar', color: 'var(--warning-color)', text: 'Scanning in Progress' },
                'completed': { icon: 'fas fa-check-circle', color: 'var(--success-color)', text: 'Scan Completed' },
                'error': { icon: 'fas fa-exclamation-triangle', color: 'var(--danger-color)', text: 'Error Occurred' }
            };

            const status = statusMap[session.status] || statusMap['completed'];

            statusElement.innerHTML = `
                <span class="status-indicator" style="background: ${status.color};"></span>
                <i class="${status.icon}" style="color: ${status.color}; margin-right: 8px;"></i>
                <strong>${status.text}</strong> - ${session.target}
            `;
        }

        function appendToTerminal(text) {
            const terminal = document.getElementById('terminal');
            terminal.textContent += text;
            terminal.scrollTop = terminal.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();

            if (!message) return;

            if (currentSessionId) {
                socket.emit('send-message', {
                    sessionId: currentSessionId,
                    message: message
                });
            } else {
                // Start new session
                startNewScan(message);
            }

            input.value = '';
        }

        async function startNewScan(target) {
            if (!target) {
                target = prompt('Enter target domain or IP:');
            }

            if (!target) return;

            try {
                const response = await fetch('/api/scan/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: target, scanType: 'auto' })
                });

                const result = await response.json();
                currentSessionId = result.sessionId;

                socket.emit('join-session', currentSessionId);

                appendToTerminal(`🚀 Started new scan session: ${currentSessionId}\n`);
                appendToTerminal(`🎯 Target: ${target}\n`);

            } catch (error) {
                console.error('Error starting scan:', error);
                appendToTerminal(`❌ Error starting scan: ${error.message}\n`);
            }
        }

        function startPassiveScan() {
            const target = prompt('Enter target for passive scan:');
            if (target) {
                startScanWithType(target, 'passive');
            }
        }

        function startActiveScan() {
            const target = prompt('Enter target for active scan:');
            if (target) {
                startScanWithType(target, 'active');
            }
        }

        async function startScanWithType(target, scanType) {
            try {
                const response = await fetch('/api/scan/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: target, scanType: scanType })
                });

                const result = await response.json();
                currentSessionId = result.sessionId;
                socket.emit('join-session', currentSessionId);

            } catch (error) {
                console.error('Error starting scan:', error);
            }
        }

        function generateReport() {
            if (currentSessionId) {
                window.open(`/api/scan/${currentSessionId}/report`, '_blank');
            } else {
                alert('No active session to generate report for');
            }
        }

        function clearSession() {
            currentSessionId = null;
            document.getElementById('chat-messages').innerHTML = '<div class="message system">🤖 Session cleared. Ready for new scan.</div>';
            document.getElementById('terminal').textContent = 'Session cleared...\n';

            // Reset metrics
            document.getElementById('subdomains-count').textContent = '0';
            document.getElementById('ports-count').textContent = '0';
            document.getElementById('vulns-count').textContent = '0';
            document.getElementById('risk-level').textContent = 'UNKNOWN';
            document.getElementById('current-target').textContent = 'No active session';
        }

        // Enter key support
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialize dashboard
        appendToTerminal('🤖 Interactive ASM Dashboard loaded\n');
        appendToTerminal('💡 Start by entering a target domain or IP\n');

        // Ensure input is visible and functional
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.focus();
                console.log('Message input found and focused');
            } else {
                console.error('Message input not found!');
            }
        });
    </script>
</body>
</html>