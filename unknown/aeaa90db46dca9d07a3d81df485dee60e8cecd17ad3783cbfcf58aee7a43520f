/**
 * Error Handling Middleware
 * Centralized error handling with proper logging and response formatting
 */

const logger = require('../utils/logger');
const { config } = require('../config');

/**
 * Custom error classes for different types of errors
 */
class AppError extends Error {
    constructor(message, statusCode, code = null) {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = true;
        
        Error.captureStackTrace(this, this.constructor);
    }
}

class ValidationError extends AppError {
    constructor(message, errors = []) {
        super(message, 400, 'VALIDATION_ERROR');
        this.errors = errors;
    }
}

class AuthenticationError extends AppError {
    constructor(message = 'Authentication required') {
        super(message, 401, 'AUTHENTICATION_ERROR');
    }
}

class AuthorizationError extends AppError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403, 'AUTHORIZATION_ERROR');
    }
}

class NotFoundError extends AppError {
    constructor(message = 'Resource not found') {
        super(message, 404, 'NOT_FOUND_ERROR');
    }
}

class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 429, 'RATE_LIMIT_ERROR');
    }
}

class ScanError extends AppError {
    constructor(message, tool = null) {
        super(message, 500, 'SCAN_ERROR');
        this.tool = tool;
    }
}

/**
 * Error handler middleware
 */
function errorHandler(err, req, res, next) {
    let error = { ...err };
    error.message = err.message;

    // Log error
    logger.error('Error occurred', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.body,
        params: req.params,
        query: req.query
    });

    // Default error response
    let response = {
        success: false,
        error: {
            message: 'Internal server error',
            code: 'INTERNAL_ERROR'
        }
    };

    // Handle specific error types
    if (error.isOperational) {
        response.error.message = error.message;
        response.error.code = error.code;
        
        if (error instanceof ValidationError) {
            response.error.errors = error.errors;
        }
        
        if (error instanceof ScanError) {
            response.error.tool = error.tool;
        }
    }

    // Handle MongoDB/Database errors
    if (error.name === 'CastError') {
        response.error.message = 'Invalid resource ID';
        response.error.code = 'INVALID_ID';
        error.statusCode = 400;
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
        response.error.message = 'Duplicate resource';
        response.error.code = 'DUPLICATE_ERROR';
        error.statusCode = 400;
    }

    // Handle JWT errors
    if (error.name === 'JsonWebTokenError') {
        response.error.message = 'Invalid token';
        response.error.code = 'INVALID_TOKEN';
        error.statusCode = 401;
    }

    if (error.name === 'TokenExpiredError') {
        response.error.message = 'Token expired';
        response.error.code = 'TOKEN_EXPIRED';
        error.statusCode = 401;
    }

    // Include stack trace in development
    if (config.server.nodeEnv === 'development') {
        response.error.stack = error.stack;
    }

    // Send error response
    res.status(error.statusCode || 500).json(response);
}

/**
 * 404 handler for unmatched routes
 */
function notFoundHandler(req, res, next) {
    const error = new NotFoundError(`Route ${req.originalUrl} not found`);
    next(error);
}

/**
 * Async error wrapper to catch async errors in route handlers
 */
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * Global unhandled promise rejection handler
 */
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Promise Rejection', {
        reason: reason.toString(),
        stack: reason.stack,
        promise: promise.toString()
    });
    
    // Close server gracefully
    process.exit(1);
});

/**
 * Global uncaught exception handler
 */
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack
    });
    
    // Close server gracefully
    process.exit(1);
});

module.exports = {
    errorHandler,
    notFoundHandler,
    asyncHandler,
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    RateLimitError,
    ScanError
};
