/**
 * Scan Routes
 * Defines all scan-related API endpoints
 */

const express = require('express');
const {
    startScan,
    getScanSession,
    executeCommand,
    runAutomatedScan,
    getAllSessions,
    deleteScanSession,
    getScanFindings,
    updateScanStatus
} = require('../controllers/scanController');
const { validateInput, createRateLimiter } = require('../../middleware/security');

const router = express.Router();

// Rate limiting for scan operations
const scanRateLimit = createRateLimiter({
    windowMs: 60000, // 1 minute
    max: 10, // 10 scans per minute
    message: {
        success: false,
        error: {
            message: 'Too many scan requests, please try again later',
            code: 'SCAN_RATE_LIMIT_EXCEEDED'
        }
    }
});

// Validation schemas
const startScanSchema = {
    body: {
        target: {
            required: true,
            type: 'domain',
            maxLength: 255,
            sanitize: true
        },
        scanType: {
            required: false,
            pattern: /^(auto|passive|active|continuous)$/
        }
    }
};

const executeCommandSchema = {
    body: {
        command: {
            required: true,
            maxLength: 1000,
            sanitize: true
        },
        tool: {
            required: false,
            maxLength: 50,
            sanitize: true
        }
    }
};

const automatedScanSchema = {
    body: {
        phase: {
            required: false,
            pattern: /^(reconnaissance|port_scan|vulnerability_assessment)$/
        }
    }
};

const updateStatusSchema = {
    body: {
        status: {
            required: true,
            pattern: /^(initializing|analyzing|scanning|completed|error|cancelled)$/
        }
    }
};

// Routes

/**
 * @route   POST /api/scan/start
 * @desc    Start a new scan session
 * @access  Public (should be protected in production)
 */
router.post('/start', 
    scanRateLimit,
    validateInput(startScanSchema),
    startScan
);

/**
 * @route   GET /api/scan/sessions
 * @desc    Get all active scan sessions
 * @access  Public (should be protected in production)
 */
router.get('/sessions', getAllSessions);

/**
 * @route   GET /api/scan/:sessionId
 * @desc    Get specific scan session details
 * @access  Public (should be protected in production)
 */
router.get('/:sessionId', getScanSession);

/**
 * @route   POST /api/scan/:sessionId/execute
 * @desc    Execute a command in a scan session
 * @access  Public (should be protected in production)
 */
router.post('/:sessionId/execute',
    validateInput(executeCommandSchema),
    executeCommand
);

/**
 * @route   POST /api/scan/:sessionId/auto
 * @desc    Run automated scan phases
 * @access  Public (should be protected in production)
 */
router.post('/:sessionId/auto',
    validateInput(automatedScanSchema),
    runAutomatedScan
);

/**
 * @route   GET /api/scan/:sessionId/findings
 * @desc    Get scan session findings with AI analysis
 * @access  Public (should be protected in production)
 */
router.get('/:sessionId/findings', getScanFindings);

/**
 * @route   PATCH /api/scan/:sessionId/status
 * @desc    Update scan session status
 * @access  Public (should be protected in production)
 */
router.patch('/:sessionId/status',
    validateInput(updateStatusSchema),
    updateScanStatus
);

/**
 * @route   DELETE /api/scan/:sessionId
 * @desc    Delete a scan session
 * @access  Public (should be protected in production)
 */
router.delete('/:sessionId', deleteScanSession);

module.exports = router;
