/**
 * Configuration Management System
 * Centralized configuration with environment variable support and validation
 */

const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

/**
 * Configuration object with default values and environment variable overrides
 */
const config = {
    // Server Configuration
    server: {
        port: parseInt(process.env.PORT) || 3000,
        host: process.env.HOST || 'localhost',
        nodeEnv: process.env.NODE_ENV || 'development',
        logLevel: process.env.LOG_LEVEL || 'info'
    },

    // AI Configuration
    ai: {
        geminiApiKey: process.env.GEMINI_API_KEY,
        model: process.env.GEMINI_MODEL || 'gemini-2.5-flash',
        maxTokens: parseInt(process.env.MAX_TOKENS) || 4096,
        temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.7
    },

    // Security Configuration
    security: {
        jwtSecret: process.env.JWT_SECRET || 'default-jwt-secret-change-in-production',
        sessionSecret: process.env.SESSION_SECRET || 'default-session-secret-change-in-production',
        encryptionKey: process.env.ENCRYPTION_KEY || 'default-encryption-key-change-in-production',
        corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
        allowedHosts: (process.env.ALLOWED_HOSTS || 'localhost,127.0.0.1').split(','),
        rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
        rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
    },

    // Database Configuration (for future implementation)
    database: {
        url: process.env.DATABASE_URL,
        redisUrl: process.env.REDIS_URL || 'redis://localhost:6379'
    },

    // External API Keys
    apiKeys: {
        abuseipdb: process.env.ABUSEIPDB_API_KEY,
        alienvault: process.env.ALIENVAULT_API_KEY,
        securitytrails: process.env.SECURITYTRAILS_API_KEY,
        shodan: process.env.SHODAN_API_KEY,
        virustotal: process.env.VIRUSTOTAL_API_KEY,
        binaryedge: process.env.BINARYEDGE_API_KEY,
        censys: {
            id: process.env.CENSYS_ID,
            secret: process.env.CENSYS_SECRET
        },
        chaos: process.env.CHAOS_API_KEY,
        github: process.env.GITHUB_API_KEY,
        hunter: process.env.HUNTER_API_KEY,
        passivetotal: {
            username: process.env.PASSIVETOTAL_USERNAME,
            apiKey: process.env.PASSIVETOTAL_API_KEY
        }
    },

    // Tool Paths
    tools: {
        nmap: process.env.NMAP_PATH || '/usr/bin/nmap',
        subfinder: process.env.SUBFINDER_PATH || '/usr/bin/subfinder',
        nuclei: process.env.NUCLEI_PATH || '/usr/bin/nuclei',
        amass: process.env.AMASS_PATH || '/usr/bin/amass',
        masscan: process.env.MASSCAN_PATH || '/usr/bin/masscan',
        gobuster: process.env.GOBUSTER_PATH || '/usr/bin/gobuster'
    },

    // Report Configuration
    reports: {
        dir: process.env.REPORTS_DIR || './scan_reports',
        templatesDir: process.env.TEMPLATES_DIR || './report_templates',
        maxSize: process.env.MAX_REPORT_SIZE || '50MB',
        retentionDays: parseInt(process.env.REPORT_RETENTION_DAYS) || 30
    },

    // Notification Configuration
    notifications: {
        slack: {
            webhookUrl: process.env.SLACK_WEBHOOK_URL
        },
        email: {
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.SMTP_PORT) || 587,
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
        }
    },

    // Scanning Configuration
    scanning: {
        maxConcurrentScans: parseInt(process.env.MAX_CONCURRENT_SCANS) || 5,
        defaultTimeout: parseInt(process.env.SCAN_TIMEOUT) || 300000, // 5 minutes
        maxTargets: parseInt(process.env.MAX_TARGETS) || 100
    }
};

/**
 * Validate required configuration
 */
function validateConfig() {
    const requiredFields = [
        'ai.geminiApiKey'
    ];

    const missingFields = [];

    requiredFields.forEach(field => {
        const value = getNestedValue(config, field);
        if (!value) {
            missingFields.push(field);
        }
    });

    if (missingFields.length > 0) {
        console.error('❌ Missing required configuration fields:');
        missingFields.forEach(field => {
            console.error(`   - ${field}`);
        });
        
        if (config.server.nodeEnv === 'production') {
            process.exit(1);
        } else {
            console.warn('⚠️  Running in development mode with missing configuration');
        }
    }
}

/**
 * Get nested object value by dot notation
 */
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
}

/**
 * Check if running in development mode
 */
function isDevelopment() {
    return config.server.nodeEnv === 'development';
}

/**
 * Check if running in production mode
 */
function isProduction() {
    return config.server.nodeEnv === 'production';
}

/**
 * Get configuration for specific module
 */
function getModuleConfig(moduleName) {
    return config[moduleName] || {};
}

// Validate configuration on load
validateConfig();

module.exports = {
    config,
    isDevelopment,
    isProduction,
    getModuleConfig,
    validateConfig
};
