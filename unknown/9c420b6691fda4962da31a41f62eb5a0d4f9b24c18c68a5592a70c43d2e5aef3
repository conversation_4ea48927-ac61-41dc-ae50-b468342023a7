/**
 * Security Middleware
 * Implements various security measures for the ASM system
 */

const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const validator = require('validator');
const { config } = require('../config');
const logger = require('../utils/logger');
const { RateLimitError, ValidationError } = require('./errorHandler');

/**
 * Rate limiting middleware
 */
const createRateLimiter = (options = {}) => {
    const defaultOptions = {
        windowMs: config.security.rateLimitWindowMs,
        max: config.security.rateLimitMaxRequests,
        message: {
            success: false,
            error: {
                message: 'Too many requests, please try again later',
                code: 'RATE_LIMIT_EXCEEDED'
            }
        },
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
            logger.securityEvent('rate_limit_exceeded', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                url: req.url
            });
            
            const error = new RateLimitError();
            res.status(error.statusCode).json({
                success: false,
                error: {
                    message: error.message,
                    code: error.code
                }
            });
        }
    };

    return rateLimit({ ...defaultOptions, ...options });
};

/**
 * CORS configuration
 */
const corsOptions = {
    origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);
        
        const allowedOrigins = config.security.corsOrigin.split(',');
        
        if (allowedOrigins.includes(origin) || config.server.nodeEnv === 'development') {
            callback(null, true);
        } else {
            logger.securityEvent('cors_violation', { origin });
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

/**
 * Helmet security headers configuration
 */
const helmetOptions = {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "ws:", "wss:"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"]
        }
    },
    crossOriginEmbedderPolicy: false
};

/**
 * Input validation middleware
 */
const validateInput = (schema) => {
    return (req, res, next) => {
        const errors = [];
        
        // Validate request body
        if (schema.body) {
            for (const [field, rules] of Object.entries(schema.body)) {
                const value = req.body[field];
                
                if (rules.required && (!value || value.trim() === '')) {
                    errors.push(`${field} is required`);
                    continue;
                }
                
                if (value) {
                    // Type validation
                    if (rules.type === 'email' && !validator.isEmail(value)) {
                        errors.push(`${field} must be a valid email`);
                    }
                    
                    if (rules.type === 'url' && !validator.isURL(value)) {
                        errors.push(`${field} must be a valid URL`);
                    }
                    
                    if (rules.type === 'domain' && !validator.isFQDN(value)) {
                        errors.push(`${field} must be a valid domain`);
                    }
                    
                    if (rules.type === 'ip' && !validator.isIP(value)) {
                        errors.push(`${field} must be a valid IP address`);
                    }
                    
                    // Length validation
                    if (rules.minLength && value.length < rules.minLength) {
                        errors.push(`${field} must be at least ${rules.minLength} characters`);
                    }
                    
                    if (rules.maxLength && value.length > rules.maxLength) {
                        errors.push(`${field} must be no more than ${rules.maxLength} characters`);
                    }
                    
                    // Pattern validation
                    if (rules.pattern && !rules.pattern.test(value)) {
                        errors.push(`${field} format is invalid`);
                    }
                    
                    // Sanitization
                    if (rules.sanitize) {
                        req.body[field] = validator.escape(value);
                    }
                }
            }
        }
        
        // Validate query parameters
        if (schema.query) {
            for (const [field, rules] of Object.entries(schema.query)) {
                const value = req.query[field];
                
                if (rules.required && !value) {
                    errors.push(`Query parameter ${field} is required`);
                }
                
                if (value && rules.type === 'number' && !validator.isNumeric(value)) {
                    errors.push(`Query parameter ${field} must be a number`);
                }
            }
        }
        
        if (errors.length > 0) {
            return next(new ValidationError('Validation failed', errors));
        }
        
        next();
    };
};

/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    // Add custom security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    next();
};

/**
 * Request logging middleware
 */
const requestLogger = (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
        const duration = Date.now() - start;
        
        logger.info('HTTP Request', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            contentLength: res.get('Content-Length')
        });
    });
    
    next();
};

/**
 * IP whitelist middleware (for admin endpoints)
 */
const ipWhitelist = (allowedIPs = []) => {
    return (req, res, next) => {
        const clientIP = req.ip;
        
        if (allowedIPs.length === 0 || allowedIPs.includes(clientIP)) {
            return next();
        }
        
        logger.securityEvent('ip_blocked', {
            ip: clientIP,
            url: req.url,
            userAgent: req.get('User-Agent')
        });
        
        res.status(403).json({
            success: false,
            error: {
                message: 'Access denied',
                code: 'IP_NOT_ALLOWED'
            }
        });
    };
};

/**
 * Sanitize target input for security scanning
 */
const sanitizeTarget = (target) => {
    // Remove potentially dangerous characters
    const sanitized = target.replace(/[;&|`$(){}[\]\\]/g, '');
    
    // Validate domain or IP format
    if (!validator.isFQDN(sanitized) && !validator.isIP(sanitized)) {
        throw new ValidationError('Invalid target format');
    }
    
    return sanitized;
};

module.exports = {
    createRateLimiter,
    corsOptions,
    helmetOptions,
    validateInput,
    securityHeaders,
    requestLogger,
    ipWhitelist,
    sanitizeTarget,
    cors: cors(corsOptions),
    helmet: helmet(helmetOptions)
};
