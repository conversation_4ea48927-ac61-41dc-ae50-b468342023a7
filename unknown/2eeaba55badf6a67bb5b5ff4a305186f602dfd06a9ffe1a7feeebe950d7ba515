/**
 * Scanning Service Module
 * Handles all security scanning operations and tool execution
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { config } = require('../config');
const logger = require('../utils/logger');
const { ScanError, AppError } = require('../middleware/errorHandler');
const { sanitizeTarget } = require('../middleware/security');

class ScanService {
    constructor() {
        this.activeSessions = new Map();
        this.scanResults = new Map();
        this.toolPaths = config.tools;
        this.reportsDir = config.reports.dir;
        this.ensureDirectories();
    }

    ensureDirectories() {
        if (!fs.existsSync(this.reportsDir)) {
            fs.mkdirSync(this.reportsDir, { recursive: true });
        }
    }

    generateSessionId() {
        return `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    createSession(target, scanType = 'auto', options = {}) {
        try {
            const sanitizedTarget = sanitizeTarget(target);
            const sessionId = this.generateSessionId();
            
            const session = {
                id: sessionId,
                target: sanitizedTarget,
                scanType,
                status: 'initializing',
                startTime: new Date().toISOString(),
                findings: {
                    subdomains: [],
                    openPorts: [],
                    vulnerabilities: [],
                    services: [],
                    riskLevel: 'UNKNOWN'
                },
                commands: [],
                chatHistory: [],
                options
            };

            this.activeSessions.set(sessionId, session);
            logger.scanStart(sessionId, sanitizedTarget, scanType);

            return session;
        } catch (error) {
            logger.error('Failed to create scan session', { target, error: error.message });
            throw new ScanError('Failed to create scan session');
        }
    }

    getSession(sessionId) {
        return this.activeSessions.get(sessionId);
    }

    updateSession(sessionId, updates) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new AppError('Session not found', 404, 'SESSION_NOT_FOUND');
        }

        Object.assign(session, updates);
        this.activeSessions.set(sessionId, session);
        return session;
    }

    async executeCommand(sessionId, command, tool = 'unknown') {
        const session = this.getSession(sessionId);
        if (!session) {
            throw new AppError('Session not found', 404, 'SESSION_NOT_FOUND');
        }

        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            let output = '';
            let errorOutput = '';

            logger.info('Executing command', { sessionId, command, tool });

            const child = spawn('bash', ['-c', command], {
                cwd: process.cwd(),
                env: { ...process.env }
            });

            child.stdout.on('data', (data) => {
                const chunk = data.toString();
                output += chunk;
                
                // Emit real-time output if callback provided
                if (session.onOutput) {
                    session.onOutput(chunk);
                }
            });

            child.stderr.on('data', (data) => {
                const chunk = data.toString();
                errorOutput += chunk;
                
                if (session.onOutput) {
                    session.onOutput(chunk);
                }
            });

            child.on('close', (code) => {
                const duration = Date.now() - startTime;
                
                logger.commandExecuted(command, code, duration);

                const result = {
                    command,
                    tool,
                    exitCode: code,
                    output,
                    errorOutput,
                    duration,
                    timestamp: new Date().toISOString()
                };

                // Add to session command history
                session.commands.push(result);
                this.updateSession(sessionId, session);

                // Save output to file
                this.saveCommandOutput(sessionId, result);

                if (code === 0) {
                    resolve(result);
                } else {
                    const error = new ScanError(`Command failed with exit code ${code}`, tool);
                    error.commandResult = result;
                    reject(error);
                }
            });

            child.on('error', (err) => {
                const error = new ScanError(`Failed to execute command: ${err.message}`, tool);
                logger.scanError(sessionId, session.target, error);
                reject(error);
            });
        });
    }

    saveCommandOutput(sessionId, result) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `${timestamp}_${sessionId}_${result.tool}_output.log`;
            const filepath = path.join(this.reportsDir, filename);
            
            const logContent = `Command: ${result.command}
Tool: ${result.tool}
Exit Code: ${result.exitCode}
Duration: ${result.duration}ms
Timestamp: ${result.timestamp}

=== STDOUT ===
${result.output}

=== STDERR ===
${result.errorOutput}
`;

            fs.writeFileSync(filepath, logContent);
        } catch (error) {
            logger.error('Failed to save command output', { sessionId, error: error.message });
        }
    }

    async runSubdomainEnumeration(sessionId, target) {
        const commands = [
            {
                tool: 'subfinder',
                command: `${this.toolPaths.subfinder} -d ${target} -silent`,
                parser: this.parseSubfinderOutput.bind(this)
            },
            {
                tool: 'amass',
                command: `${this.toolPaths.amass} enum -passive -d ${target}`,
                parser: this.parseAmassOutput.bind(this)
            }
        ];

        const results = [];
        for (const cmd of commands) {
            try {
                const result = await this.executeCommand(sessionId, cmd.command, cmd.tool);
                const parsed = cmd.parser(result.output);
                results.push({ ...result, parsed });
                
                // Update session findings
                this.updateFindings(sessionId, 'subdomains', parsed);
            } catch (error) {
                logger.error(`${cmd.tool} execution failed`, { sessionId, error: error.message });
                results.push({ tool: cmd.tool, error: error.message });
            }
        }

        return results;
    }

    async runPortScan(sessionId, target, ports = '1-1000') {
        const command = `${this.toolPaths.nmap} -sS -sV -O ${target} -p ${ports} --open`;
        
        try {
            const result = await this.executeCommand(sessionId, command, 'nmap');
            const parsed = this.parseNmapOutput(result.output);
            
            this.updateFindings(sessionId, 'openPorts', parsed.ports);
            this.updateFindings(sessionId, 'services', parsed.services);
            
            return result;
        } catch (error) {
            logger.error('Port scan failed', { sessionId, error: error.message });
            throw error;
        }
    }

    async runVulnerabilityAssessment(sessionId, target) {
        const command = `${this.toolPaths.nuclei} -target ${target} -silent -json`;
        
        try {
            const result = await this.executeCommand(sessionId, command, 'nuclei');
            const parsed = this.parseNucleiOutput(result.output);
            
            this.updateFindings(sessionId, 'vulnerabilities', parsed);
            
            return result;
        } catch (error) {
            logger.error('Vulnerability assessment failed', { sessionId, error: error.message });
            throw error;
        }
    }

    parseSubfinderOutput(output) {
        const subdomains = output
            .split('\n')
            .filter(line => line.trim())
            .filter(line => line.includes('.'))
            .map(line => line.trim());
        
        return [...new Set(subdomains)]; // Remove duplicates
    }

    parseAmassOutput(output) {
        const subdomains = output
            .split('\n')
            .filter(line => line.trim())
            .filter(line => line.includes('.'))
            .map(line => line.trim());
        
        return [...new Set(subdomains)]; // Remove duplicates
    }

    parseNmapOutput(output) {
        const ports = [];
        const services = [];
        
        const lines = output.split('\n');
        
        for (const line of lines) {
            // Parse open ports
            const portMatch = line.match(/(\d+)\/tcp\s+open\s+(\S+)/);
            if (portMatch) {
                ports.push({
                    port: parseInt(portMatch[1]),
                    protocol: 'tcp',
                    state: 'open',
                    service: portMatch[2]
                });
            }
            
            // Parse service versions
            const serviceMatch = line.match(/(\d+)\/tcp\s+open\s+(\S+)\s+(.+)/);
            if (serviceMatch) {
                services.push({
                    port: parseInt(serviceMatch[1]),
                    service: serviceMatch[2],
                    version: serviceMatch[3].trim()
                });
            }
        }
        
        return { ports, services };
    }

    parseNucleiOutput(output) {
        const vulnerabilities = [];
        
        const lines = output.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
            try {
                const vuln = JSON.parse(line);
                vulnerabilities.push({
                    id: vuln.templateID || vuln.template,
                    name: vuln.info?.name || 'Unknown',
                    severity: vuln.info?.severity || 'info',
                    description: vuln.info?.description || '',
                    matched: vuln.matched || vuln.matchedAt,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                // Skip invalid JSON lines
                continue;
            }
        }
        
        return vulnerabilities;
    }

    updateFindings(sessionId, type, data) {
        const session = this.getSession(sessionId);
        if (!session) return;

        if (Array.isArray(data)) {
            session.findings[type] = [...new Set([...session.findings[type], ...data])];
        } else {
            session.findings[type].push(data);
        }

        // Update risk level based on findings
        session.findings.riskLevel = this.calculateRiskLevel(session.findings);
        
        this.updateSession(sessionId, session);
    }

    calculateRiskLevel(findings) {
        let riskScore = 0;
        
        // Score based on vulnerabilities
        const criticalVulns = findings.vulnerabilities.filter(v => v.severity === 'critical').length;
        const highVulns = findings.vulnerabilities.filter(v => v.severity === 'high').length;
        const mediumVulns = findings.vulnerabilities.filter(v => v.severity === 'medium').length;
        
        riskScore += criticalVulns * 10;
        riskScore += highVulns * 5;
        riskScore += mediumVulns * 2;
        
        // Score based on open ports
        const dangerousPorts = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995];
        const openDangerousPorts = findings.openPorts.filter(p => 
            dangerousPorts.includes(p.port)
        ).length;
        
        riskScore += openDangerousPorts * 1;
        
        // Determine risk level
        if (riskScore >= 20) return 'CRITICAL';
        if (riskScore >= 10) return 'HIGH';
        if (riskScore >= 5) return 'MEDIUM';
        if (riskScore > 0) return 'LOW';
        
        return 'UNKNOWN';
    }

    getAllSessions() {
        return Array.from(this.activeSessions.values());
    }

    deleteSession(sessionId) {
        const deleted = this.activeSessions.delete(sessionId);
        if (deleted) {
            logger.info('Session deleted', { sessionId });
        }
        return deleted;
    }
}

// Create singleton instance
const scanService = new ScanService();

module.exports = scanService;
