# =============================================================================
# ASM System Environment Configuration
# =============================================================================
# Copy this file to .env and fill in your actual API keys and configuration

# =============================================================================
# AI Configuration
# =============================================================================
GEMINI_API_KEY=your_google_gemini_api_key_here

# =============================================================================
# Server Configuration
# =============================================================================
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# =============================================================================
# Database Configuration (Future Implementation)
# =============================================================================
# DATABASE_URL=postgresql://username:password@localhost:5432/asm_db
# REDIS_URL=redis://localhost:6379

# =============================================================================
# Security Configuration
# =============================================================================
JWT_SECRET=your_jwt_secret_key_here
SESSION_SECRET=your_session_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# =============================================================================
# External API Keys for Security Tools
# =============================================================================

# AbuseIPDB - https://www.abuseipdb.com/account
ABUSEIPDB_API_KEY=your_abuseipdb_api_key_here

# AlienVault OTX - https://otx.alienvault.com/
ALIENVAULT_API_KEY=your_alienvault_api_key_here

# SecurityTrails - https://securitytrails.com/
SECURITYTRAILS_API_KEY=your_securitytrails_api_key_here

# Shodan - https://account.shodan.io/
SHODAN_API_KEY=your_shodan_api_key_here

# VirusTotal - https://www.virustotal.com/
VIRUSTOTAL_API_KEY=your_virustotal_api_key_here

# BinaryEdge - https://app.binaryedge.io/account
BINARYEDGE_API_KEY=your_binaryedge_api_key_here

# Censys - https://censys.io/register
CENSYS_ID=your_censys_id_here
CENSYS_SECRET=your_censys_secret_here

# Chaos - https://chaos.projectdiscovery.io/
CHAOS_API_KEY=your_chaos_api_key_here

# GitHub - https://github.com/settings/tokens
GITHUB_API_KEY=your_github_api_key_here

# Hunter - https://hunter.io/
HUNTER_API_KEY=your_hunter_api_key_here

# PassiveTotal - https://community.riskiq.com/
PASSIVETOTAL_USERNAME=your_passivetotal_username_here
PASSIVETOTAL_API_KEY=your_passivetotal_api_key_here

# =============================================================================
# Notification Configuration
# =============================================================================
# Slack webhook for notifications
SLACK_WEBHOOK_URL=your_slack_webhook_url_here

# Email configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password_here

# =============================================================================
# Parrot OS Tool Paths
# =============================================================================
NMAP_PATH=/usr/bin/nmap
SUBFINDER_PATH=/usr/bin/subfinder
NUCLEI_PATH=/usr/bin/nuclei
AMASS_PATH=/usr/bin/amass
MASSCAN_PATH=/usr/bin/masscan
GOBUSTER_PATH=/usr/bin/gobuster

# =============================================================================
# Report Configuration
# =============================================================================
REPORTS_DIR=./scan_reports
TEMPLATES_DIR=./report_templates
MAX_REPORT_SIZE=50MB
REPORT_RETENTION_DAYS=30

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# Security Headers Configuration
# =============================================================================
CORS_ORIGIN=http://localhost:3000
ALLOWED_HOSTS=localhost,127.0.0.1
