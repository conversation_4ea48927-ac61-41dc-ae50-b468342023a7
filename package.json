{"name": "gemini-black", "version": "2.0.0", "description": "Enhanced Interactive ASM System with AI-powered attack surface management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "prod": "NODE_ENV=production node server.js", "legacy:interactive": "node interactive.js", "legacy:enhanced": "node enhanced_ai_asm.js", "legacy:web": "node web_dashboard.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "setup": "cp .env.example .env && echo 'Please configure your .env file'"}, "keywords": ["asm", "attack-surface-management", "pentest", "security", "ai", "gemini", "parrot-os", "vulnerability-assessment", "reconnaissance"], "author": "s4ng", "license": "ISC", "engines": {"node": ">=16.0.0"}, "dependencies": {"@google/generative-ai": "^0.24.1", "cors": "^2.8.5", "docxtemplater": "^3.65.2", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^7.2.0", "html-pdf-node": "^1.0.8", "jspdf": "^3.0.1", "multer": "^2.0.1", "pizzip": "^3.2.0", "puppeteer": "^24.12.1", "socket.io": "^4.8.1", "validator": "^13.15.15", "yargs": "^17.7.2"}, "devDependencies": {"nodemon": "^3.0.2"}}