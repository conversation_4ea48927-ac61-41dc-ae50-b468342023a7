# 🤖 Enhanced Interactive ASM System

A next-generation Attack Surface Management platform with AI-powered intelligence, modular architecture, and enhanced security features designed for Parrot OS Security Edition.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- Parrot OS Security Edition (recommended)
- Security tools: nmap, subfinder, nuclei, amass

### Installation

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd gemini-black
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Start the System**
   ```bash
   # Enhanced modular system (recommended)
   npm start

   # Development mode with auto-reload
   npm run dev

   # Legacy systems (for compatibility)
   npm run legacy:web
   npm run legacy:enhanced
   npm run legacy:interactive
   ```

4. **Access Dashboard**
   - Web Interface: http://localhost:3000
   - API Documentation: http://localhost:3000/api/status
   - Health Check: http://localhost:3000/health

## 🏗️ Architecture Overview

### New Modular Structure
```
src/
├── api/                 # API layer
│   ├── controllers/     # Route handlers
│   ├── middleware/      # Custom middleware
│   └── routes/          # Route definitions
├── core/                # Core business logic
│   ├── scanner/         # Scanning engine
│   ├── ai/              # AI integration
│   └── analyzer/        # Result analysis
├── services/            # External services
│   ├── aiService.js     # AI integration service
│   ├── scanService.js   # Scanning operations
│   └── reportService.js # Report generation
├── config/              # Configuration management
├── utils/               # Utility functions
└── middleware/          # Security & error handling
```

### Key Improvements

#### 🔒 **Enhanced Security**
- Environment-based configuration
- Input validation and sanitization
- Rate limiting and CORS protection
- Security headers with Helmet.js
- Proper error handling and logging

#### 🧠 **Advanced AI Integration**
- Intelligent target analysis
- Real-time command output processing
- Adaptive scanning strategies
- Risk assessment and prioritization
- Interactive chat interface

#### 📊 **Modern Dashboard**
- Responsive design with CSS Grid
- Real-time WebSocket updates
- Interactive metrics and visualizations
- Enhanced UX with Font Awesome icons
- Professional color scheme and animations

#### 🛠️ **Improved Backend**
- Modular service architecture
- Comprehensive error handling
- Structured logging system
- RESTful API design
- Session management

## 🎯 Features

### Core Capabilities
- **AI-Powered Analysis**: Google Gemini integration for intelligent decision making
- **Multi-Tool Integration**: Nmap, Subfinder, Nuclei, Amass support
- **Real-time Monitoring**: Live command execution and output streaming
- **Interactive Dashboard**: Modern web interface with real-time updates
- **Comprehensive Reporting**: Multiple format support (HTML, PDF, JSON, DOCX)

### Security Features
- **Input Validation**: Comprehensive validation for all inputs
- **Rate Limiting**: Protection against abuse and DoS
- **CORS Protection**: Configurable cross-origin resource sharing
- **Security Headers**: Helmet.js integration for security headers
- **Error Handling**: Structured error responses and logging

### AI Capabilities
- **Target Analysis**: Intelligent assessment of scan targets
- **Strategy Generation**: Adaptive scanning approach based on findings
- **Risk Assessment**: Real-time risk level calculation
- **Command Suggestion**: AI-recommended next steps
- **Interactive Chat**: Natural language interaction with AI

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```bash
# AI Configuration
GEMINI_API_KEY=your_google_gemini_api_key

# Server Configuration
PORT=3000
NODE_ENV=development

# Security Configuration
JWT_SECRET=your_jwt_secret
CORS_ORIGIN=http://localhost:3000

# External API Keys
SHODAN_API_KEY=your_shodan_key
VIRUSTOTAL_API_KEY=your_virustotal_key
# ... (see .env.example for full list)
```

### Tool Paths
Configure security tool paths:
```bash
NMAP_PATH=/usr/bin/nmap
SUBFINDER_PATH=/usr/bin/subfinder
NUCLEI_PATH=/usr/bin/nuclei
AMASS_PATH=/usr/bin/amass
```

## 📡 API Reference

### Scan Operations
- `POST /api/scan/start` - Start new scan session
- `GET /api/scan/:sessionId` - Get session details
- `POST /api/scan/:sessionId/execute` - Execute command
- `POST /api/scan/:sessionId/auto` - Run automated scan
- `GET /api/scan/:sessionId/findings` - Get findings with AI analysis

### System Status
- `GET /health` - System health check
- `GET /api/status` - API status and metrics

## 🔍 Usage Examples

### Starting a Basic Scan
```javascript
// Via API
const response = await fetch('/api/scan/start', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        target: 'example.com',
        scanType: 'auto'
    })
});
```

### Interactive AI Chat
```javascript
// Send message to AI
socket.emit('send-message', {
    sessionId: 'scan_123',
    message: 'What should I scan next?'
});
```

## 🛡️ Parrot OS Integration

### Recommended Tools
The system integrates with Parrot OS security tools:
- **Nmap**: Network discovery and port scanning
- **Subfinder**: Subdomain enumeration
- **Nuclei**: Vulnerability assessment
- **Amass**: Attack surface mapping
- **Masscan**: High-speed port scanning

### Security Best Practices
- Run with appropriate privileges
- Configure firewall rules
- Use VPN for external scanning
- Follow responsible disclosure
- Respect rate limits and ToS

## 📈 Monitoring and Logging

### Log Levels
- **ERROR**: Critical errors and failures
- **WARN**: Warning conditions and security events
- **INFO**: General information and scan progress
- **DEBUG**: Detailed debugging information

### Log Files
- `logs/combined.log` - All log entries
- `logs/error.log` - Error-level logs only
- `scan_reports/` - Individual scan outputs

## 🔄 Migration from Legacy System

### Compatibility
The new system maintains compatibility with legacy components:
- Use `npm run legacy:web` for old web dashboard
- Use `npm run legacy:enhanced` for enhanced CLI
- Use `npm run legacy:interactive` for original CLI

### Migration Steps
1. Backup existing configuration and reports
2. Install new dependencies: `npm install`
3. Configure environment: `cp .env.example .env`
4. Test new system: `npm run dev`
5. Migrate custom configurations
6. Update automation scripts

## 🤝 Contributing

### Development Setup
```bash
# Install dependencies
npm install

# Start in development mode
npm run dev

# Run tests (when available)
npm test
```

### Code Style
- Use ES6+ features
- Follow modular architecture
- Add comprehensive error handling
- Include JSDoc comments
- Maintain security best practices

## 📄 License

ISC License - See LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the logs in `logs/` directory
2. Review configuration in `.env`
3. Verify tool installations and paths
4. Check API endpoints with `/health`

---

**⚠️ Security Notice**: This tool is designed for authorized security testing only. Always ensure you have proper authorization before scanning any targets.
